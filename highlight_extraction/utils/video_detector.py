#!/usr/bin/env python3
"""
Video Highlights Detection System

Advanced highlight detection algorithm designed to find the most engaging,
valuable, and shareable moments in any video content.

Core Philosophy:
- Content quality over keyword matching
- Engagement potential over technical metrics
- Simplicity over complexity
- Universal applicability over niche optimization

Algorithm:
1. Content Analysis: Analyze transcript for engagement signals
2. Momentum Detection: Find moments of high information density
3. Emotional Peaks: Identify emotional highs and compelling moments
4. Natural Boundaries: Respect speech patterns and natural breaks
5. Optimal Selection: Choose best moments with diversity and flow
"""

import re
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)

# Import validation utilities for consistent minimum duration enforcement
try:
    from utils.validation_utils import HighlightValidationUtils
    from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS
except ImportError:
    # Fallback if imports fail
    HighlightValidationUtils = None
    MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

class VideoHighlightsDetector:
    """
    Advanced video highlights detection system.

    Finds the most engaging moments in any video by analyzing:
    - Speech patterns and energy
    - Information density and value
    - Emotional engagement signals
    - Natural conversation flow
    - Content uniqueness and impact
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Core parameters for finding optimal scenes
        self.min_highlight_duration = 10.0  # Minimum 10 seconds for impact
        self.max_highlight_duration = 30.0  # Maximum 30 seconds for attention span
        self.target_total_duration = 300.0  # Target 5 minutes of highlights (much more generous)
        self.overlap_threshold = 1.0        # Minimum gap between highlights (allow closer spacing)

        # Engagement signal patterns (advanced detection algorithms)
        self.engagement_patterns = {
            # High-value question words that create curiosity
            'curiosity_triggers': [
                'what if', 'imagine', 'picture this', 'here\'s the thing',
                'the secret', 'the truth', 'nobody tells you', 'most people don\'t',
                'the real reason', 'what really happens', 'the problem is',
                'insight', 'we think', 'you can\'t', 'but we can', 'exactly',
                'i think', 'to add to that', 'the fastest way', 'in fact',
                'you know', 'the way that', 'they realize', 'the number one',
                'i heard', 'i like the story', 'where they', 'what they do'
            ],

            # Emotional intensity indicators
            'emotional_peaks': [
                'incredible', 'amazing', 'unbelievable', 'shocking', 'surprising',
                'devastating', 'heartbreaking', 'hilarious', 'terrifying',
                'mind-blowing', 'life-changing', 'game-changing'
            ],

            # Authority and credibility signals
            'authority_signals': [
                'research shows', 'studies prove', 'data reveals', 'experts say',
                'i discovered', 'i learned', 'i realized', 'the fact is',
                'what we found', 'the results show', 'absolutely', 'alanda barton says',
                'dale carnegie', 'michelle thomas', 'jeffrey miller', 'famous thread',
                'reddit', 'bore has famously wrote', 'my cousin said', 'taleb'
            ],

            # Story and narrative hooks
            'story_hooks': [
                'let me tell you', 'here\'s what happened', 'i remember when',
                'there was this time', 'picture this', 'imagine you\'re',
                'so there i was', 'it all started when'
            ],

            # Conflict and tension builders
            'tension_builders': [
                'but here\'s the problem', 'the issue is', 'what went wrong',
                'the mistake', 'the failure', 'the challenge', 'the struggle',
                'but then', 'suddenly', 'unexpectedly'
            ],

            # Resolution and insight moments
            'insight_moments': [
                'that\'s when i realized', 'the breakthrough came', 'it clicked',
                'suddenly it made sense', 'the answer was', 'i figured out',
                'the solution is', 'here\'s the key', 'change', 'relationship',
                'love', 'connection', 'unity', 'wholeness', 'decision', 'choice',
                'heuristic', 'path', 'painful', 'equanimous', 'peace', 'mental peace',
                'iterate', 'leverage', 'opportunities', 'friend circle', 'dating pool',
                'job opportunities', 'quality of life', 'genetics', 'behavior',
                'temperament', 'values', 'compromise', 'loss aversion', 'starting over',
                'successful people', 'mountain climbing', 'trauma', 'insight',
                'schedule', 'alienate', 'self-conscious', 'compliment', 'criticize',
                'confidence', 'passive observer', 'objective', 'threatened', 'fearful',
                'praiseworthy', 'authenticity', 'potential', 'resume', 'basis',
                'ineffable', 'consciousness', 'desire for unity', 'god-shaped hole',
                'mysticism', 'awe', 'sistine chapel', 'emotion', 'craves'
            ]
        }

        # Speech energy indicators
        self.energy_indicators = {
            'high_energy': ['!', 'really', 'absolutely', 'definitely', 'exactly', 'totally'],
            'emphasis': ['very', 'extremely', 'incredibly', 'massively', 'hugely'],
            'certainty': ['always', 'never', 'every', 'all', 'none', 'everything', 'nothing']
        }

        self.logger.info("Advanced Highlights Detector initialized")

    def find_best_highlights(self, transcript_segments: List[Dict[str, Any]],
                           video_duration: Optional[float] = None,
                           max_highlights: Optional[int] = None,
                           min_duration_override: Optional[float] = None,
                           max_duration_override: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Find the optimal highlights in the video.

        Args:
            transcript_segments: List of transcript segments with timing
            video_duration: Total video duration (optional)
            max_highlights: Maximum number of highlights to generate (optional, defaults to 100 for production, 1 for testing)
            min_duration_override: Optional override for minimum highlight duration.
            max_duration_override: Optional override for maximum highlight duration.

        Returns:
            List of the optimal highlight segments with scores and metadata
        """
        if not transcript_segments:
            return []

        # Store original durations and apply overrides if provided
        original_min_highlight_duration = self.min_highlight_duration
        original_max_highlight_duration = self.max_highlight_duration

        if min_duration_override is not None:
            self.min_highlight_duration = float(min_duration_override)
            self.logger.info(f"Overriding min_highlight_duration to: {self.min_highlight_duration}s")
        if max_duration_override is not None:
            self.max_highlight_duration = float(max_duration_override)
            self.logger.info(f"Overriding max_highlight_duration to: {self.max_highlight_duration}s")

        # Ensure min_highlight_duration is used consistently
        self.logger.info(f"Using min_highlight_duration: {self.min_highlight_duration}s for this run.")

        if max_highlights is not None:
            if max_highlights == 1:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments to find the single optimal highlight")
            elif max_highlights <= 3:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments for up to {max_highlights} highlights")
            else:
                self.logger.info(f"Analyzing {len(transcript_segments)} segments for advanced highlights (max: {max_highlights})")
        else:
            self.logger.info(f"Analyzing {len(transcript_segments)} segments for advanced highlights")

        # Step 1: Analyze each segment for engagement potential
        segment_scores = self._analyze_segment_engagement(transcript_segments)

        # Step 2: Find momentum peaks (high information density moments)
        momentum_peaks = self._find_momentum_peaks(transcript_segments, segment_scores)

        # Step 3: Detect emotional and narrative peaks
        narrative_peaks = self._find_narrative_peaks(transcript_segments)

        # Step 4: Generate candidate highlight windows
        candidates = self._generate_highlight_candidates(
            transcript_segments, segment_scores, momentum_peaks, narrative_peaks
        )

        # Step 5: Score and rank all candidates
        scored_candidates = self._score_highlight_candidates(candidates, transcript_segments)

        # Step 6: Select optimal set of non-overlapping highlights
        final_highlights = self._select_optimal_highlights(scored_candidates, max_highlights)

        self.logger.info(f"Selected {len(final_highlights)} advanced highlights "
                        f"({sum(h['duration'] for h in final_highlights):.1f}s total)")

        # Restore original durations
        self.min_highlight_duration = original_min_highlight_duration
        self.max_highlight_duration = original_max_highlight_duration

        return final_highlights

    def _analyze_segment_engagement(self, transcript_segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Analyze each segment for engagement potential using pattern matching.

        Args:
            transcript_segments: List of transcript segments

        Returns:
            Dictionary mapping segment index to engagement score (0.0-1.0)
        """
        segment_scores = {}

        for i, segment in enumerate(transcript_segments):
            text = segment.get('text', '').lower()
            score = 0.0

            # Check for engagement patterns
            for pattern_type, patterns in self.engagement_patterns.items():
                for pattern in patterns:
                    if pattern in text:
                        # Weight different pattern types
                        if pattern_type == 'curiosity_triggers':
                            score += 0.3
                        elif pattern_type == 'emotional_peaks':
                            score += 0.25
                        elif pattern_type == 'authority_signals':
                            score += 0.2
                        elif pattern_type == 'story_hooks':
                            score += 0.15
                        elif pattern_type == 'tension_builders':
                            score += 0.15
                        elif pattern_type == 'insight_moments':
                            score += 0.2

            # Check for energy indicators
            for energy_type, indicators in self.energy_indicators.items():
                for indicator in indicators:
                    if indicator in text:
                        if energy_type == 'high_energy':
                            score += 0.1
                        elif energy_type == 'emphasis':
                            score += 0.05
                        elif energy_type == 'certainty':
                            score += 0.05

            # Normalize score to 0-1 range
            segment_scores[i] = min(1.0, score)

        return segment_scores

    def _find_momentum_peaks(self, transcript_segments: List[Dict[str, Any]],
                           segment_scores: Dict[int, float]) -> List[int]:
        """
        Find segments with high information density and momentum.

        Args:
            transcript_segments: List of transcript segments
            segment_scores: Engagement scores for each segment

        Returns:
            List of segment indices representing momentum peaks
        """
        if len(segment_scores) < 3:
            return list(segment_scores.keys())

        # Calculate moving average of scores
        window_size = 3
        momentum_peaks = []

        for i in range(len(transcript_segments)):
            if i < window_size - 1:
                continue

            # Calculate average score in window
            window_scores = [segment_scores.get(j, 0.0) for j in range(i - window_size + 1, i + 1)]
            avg_score = sum(window_scores) / len(window_scores)

            # Check if this is a local peak
            if avg_score > 0.3:  # Threshold for momentum
                momentum_peaks.append(i)

        return momentum_peaks

    def _find_narrative_peaks(self, transcript_segments: List[Dict[str, Any]]) -> List[int]:
        """
        Find segments that represent narrative or emotional peaks.

        Args:
            transcript_segments: List of transcript segments

        Returns:
            List of segment indices representing narrative peaks
        """
        narrative_peaks = []

        for i, segment in enumerate(transcript_segments):
            text = segment.get('text', '').lower()

            # Look for narrative markers
            narrative_markers = [
                'that\'s when', 'suddenly', 'then i realized', 'the moment',
                'breakthrough', 'turning point', 'game changer', 'life changing',
                'incredible', 'amazing', 'unbelievable', 'shocking'
            ]

            for marker in narrative_markers:
                if marker in text:
                    narrative_peaks.append(i)
                    break

        return narrative_peaks

    def _generate_highlight_candidates(self, transcript_segments: List[Dict[str, Any]],
                                     segment_scores: Dict[int, float],
                                     momentum_peaks: List[int],
                                     narrative_peaks: List[int]) -> List[Dict[str, Any]]:
        """
        Generate candidate highlight windows based on peaks and scores.

        Args:
            transcript_segments: List of transcript segments
            segment_scores: Engagement scores for each segment
            momentum_peaks: List of momentum peak indices
            narrative_peaks: List of narrative peak indices

        Returns:
            List of candidate highlight dictionaries
        """
        candidates = []
        all_peaks = set(momentum_peaks + narrative_peaks)

        for peak_idx in all_peaks:
            if peak_idx >= len(transcript_segments):
                continue

            # Generate windows of different sizes around each peak
            for window_size in [3, 5, 7, 10]:  # Number of segments
                start_idx = max(0, peak_idx - window_size // 2)
                end_idx = min(len(transcript_segments) - 1, peak_idx + window_size // 2)

                if end_idx <= start_idx:
                    continue

                # Calculate timing
                start_time = transcript_segments[start_idx].get('start', 0.0)
                end_time = transcript_segments[end_idx].get('end', start_time + 10.0)
                duration = end_time - start_time

                # Check duration constraints
                if duration < self.min_highlight_duration or duration > self.max_highlight_duration:
                    continue

                # Combine text from segments
                text_parts = []
                for i in range(start_idx, end_idx + 1):
                    text_parts.append(transcript_segments[i].get('text', ''))
                combined_text = ' '.join(text_parts)

                candidate = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'start_segment_idx': start_idx,
                    'end_segment_idx': end_idx,
                    'peak_idx': peak_idx,
                    'text': combined_text,
                    'window_size': window_size
                }

                candidates.append(candidate)

        return candidates

    def _score_highlight_candidates(self, candidates: List[Dict[str, Any]],
                                  transcript_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Score and rank highlight candidates.

        Args:
            candidates: List of candidate highlight dictionaries
            transcript_segments: List of transcript segments

        Returns:
            List of scored candidates sorted by score (highest first)
        """
        scored_candidates = []

        for candidate in candidates:
            score = self._calculate_candidate_score(candidate, transcript_segments)
            candidate['engagement_score'] = score
            scored_candidates.append(candidate)

        # Sort by score (highest first)
        scored_candidates.sort(key=lambda x: x['engagement_score'], reverse=True)

        return scored_candidates

    def _calculate_candidate_score(self, candidate: Dict[str, Any],
                                 transcript_segments: List[Dict[str, Any]]) -> float:
        """
        Calculate engagement score for a candidate highlight.

        Args:
            candidate: Candidate highlight dictionary
            transcript_segments: List of transcript segments

        Returns:
            Engagement score (0.0-1.0)
        """
        text = candidate['text'].lower()
        score = 0.0

        # Base engagement from pattern matching
        for pattern_type, patterns in self.engagement_patterns.items():
            pattern_count = sum(1 for pattern in patterns if pattern in text)
            if pattern_count > 0:
                if pattern_type == 'curiosity_triggers':
                    score += min(0.4, pattern_count * 0.1)
                elif pattern_type == 'emotional_peaks':
                    score += min(0.3, pattern_count * 0.1)
                elif pattern_type == 'authority_signals':
                    score += min(0.2, pattern_count * 0.05)
                elif pattern_type == 'story_hooks':
                    score += min(0.2, pattern_count * 0.05)
                elif pattern_type == 'tension_builders':
                    score += min(0.2, pattern_count * 0.05)
                elif pattern_type == 'insight_moments':
                    score += min(0.3, pattern_count * 0.05)

        # Energy indicators
        for energy_type, indicators in self.energy_indicators.items():
            energy_count = sum(1 for indicator in indicators if indicator in text)
            if energy_count > 0:
                score += min(0.1, energy_count * 0.02)

        # Duration bonus (prefer optimal length)
        duration = candidate['duration']
        if 15.0 <= duration <= 25.0:  # Sweet spot
            score += 0.1
        elif 10.0 <= duration <= 30.0:  # Acceptable range
            score += 0.05

        # Text length bonus (prefer substantial content)
        word_count = len(text.split())
        if word_count >= 50:
            score += 0.1
        elif word_count >= 30:
            score += 0.05

        return min(1.0, score)

    def _select_optimal_highlights(self, scored_candidates: List[Dict[str, Any]],
                                 max_highlights: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Select the optimal set of non-overlapping highlights.

        Args:
            scored_candidates: List of scored candidate highlights
            max_highlights: Maximum number of highlights to select

        Returns:
            List of selected optimal highlights
        """
        if not scored_candidates:
            return []

        # Set default max_highlights if not provided
        if max_highlights is None:
            max_highlights = 100  # Default for production

        selected_highlights = []
        used_time_ranges = []

        for candidate in scored_candidates:
            if len(selected_highlights) >= max_highlights:
                break

            start_time = candidate['start_time']
            end_time = candidate['end_time']

            # Check for overlap with already selected highlights
            has_overlap = False
            for used_start, used_end in used_time_ranges:
                # Check if there's significant overlap (more than overlap_threshold)
                overlap_start = max(start_time, used_start)
                overlap_end = min(end_time, used_end)
                overlap_duration = max(0, overlap_end - overlap_start)

                if overlap_duration > self.overlap_threshold:
                    has_overlap = True
                    break

            if not has_overlap:
                # Apply minimum duration validation if available
                duration = candidate['duration']
                if HighlightValidationUtils:
                    if not HighlightValidationUtils.validate_highlight_duration(duration):
                        self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                        continue
                else:
                    # Fallback validation
                    if duration < MIN_HIGHLIGHT_DURATION_SECONDS:
                        self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                        continue

                # Create final highlight format
                highlight = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'engagement_score': candidate['engagement_score'],
                    'text': candidate.get('text', ''),
                    'start_segment_idx': candidate.get('start_segment_idx'),
                    'end_segment_idx': candidate.get('end_segment_idx'),
                    'algorithm': 'advanced_content_analysis',
                    'metadata': {
                        'peak_idx': candidate.get('peak_idx'),
                        'window_size': candidate.get('window_size'),
                        'score_breakdown': {
                            'engagement': candidate['engagement_score']
                        }
                    }
                }

                selected_highlights.append(highlight)
                used_time_ranges.append((start_time, end_time))

        # Sort final highlights by start time
        selected_highlights.sort(key=lambda x: x['start_time'])

        # Log selection summary
        total_duration = sum(h['duration'] for h in selected_highlights)
        avg_score = sum(h['engagement_score'] for h in selected_highlights) / len(selected_highlights) if selected_highlights else 0

        self.logger.info(f"Selected {len(selected_highlights)} highlights:")
        self.logger.info(f"  Total duration: {total_duration:.1f}s")
        self.logger.info(f"  Average engagement score: {avg_score:.3f}")

        return selected_highlights
