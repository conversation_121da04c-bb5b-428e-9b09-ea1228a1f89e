#!/usr/bin/env python3
"""
Configuration module for highlight extraction
"""

from .settings import *

__all__ = [
    'DEFAULT_TARGET_LENGTH',
    'MIN_SPAN_DURATION', 
    '<PERSON>X_<PERSON>AN_DURATION',
    'PADDING_SECONDS',
    'SCORING_WEIGHTS',
    'MIN_QUALITY_SCORE',
    'MIN_WORDS_PER_SPAN',
    'MAX_SILENCE_RATIO',
    'QA_COVERAGE_THRESHOLD',
    'MIN_QA_QUALITY_SCORE',
    'MIN_ANSWER_LENGTH',
    'OUTPUT_RESOLUTION',
    'OUTPUT_FORMAT',
    'OUTPUT_CODEC',
    'OUTPUT_QUALITY',
    'LOG_LEVEL',
    'LOG_FORMAT',
    'OPENAI_ENABLED',
    'OPENAI_MODEL',
    'OPENAI_MAX_REQUESTS',
    'USE_GPU',
    'GPU_MEMORY_LIMIT',
    'HIGHLIGHTS_OUTPUT_DIR',
    'HIGHLIGHTS_LOGS_DIR',
    'MAX_CONCURRENT_JOBS',
    'JOB_TIMEOUT_SECONDS',
    'JOB_CLEANUP_DAYS'
]
