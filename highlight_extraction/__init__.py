#!/usr/bin/env python3
"""
Highlight Extraction Module

This module provides comprehensive video highlight extraction functionality,
organized into logical components for maintainability and ease of use.

The module is structured as follows:
- core/: Core extraction engines and algorithms
- pipeline/: Pipeline task wrappers for integration with the main video processing pipeline
- utils/: Utility functions and classes for highlight processing
- jobs/: Job-based processing system for standalone highlight extraction
- config/: Configuration settings and parameters

Usage:
    # For pipeline integration
    from highlight_extraction.pipeline import IntelligentHighlightsTask

    # For standalone processing
    from highlight_extraction.jobs import HighlightsJobManager

    # For direct access to core functionality
    from highlight_extraction.core import IntelligentExtractor
"""

__version__ = "1.0.0"
__author__ = "Video Highlights Team"

# Import main classes for easy access (lazy loading to avoid heavy dependencies)
def _lazy_import():
    """Lazy import function to avoid loading heavy dependencies at module import time"""
    global IntelligentExtractor, QAExtractor
    global IntelligentHighlightsTask, QAHighlightsTask
    global HighlightsJobManager
    global HighlightsScorer, VideoHighlightsDetector, IFrameExtractor, QuestionIdentifier

    from .core import (
        IntelligentExtractor,
        QAExtractor
    )

    from .pipeline import (
        IntelligentHighlightsTask,
        QAHighlightsTask
    )

    from .jobs import HighlightsJobManager

    from .utils import (
        HighlightsScorer,
        VideoHighlightsDetector,
        IFrameExtractor,
        QuestionIdentifier
    )

# Initialize as None - will be loaded when first accessed
IntelligentExtractor = None
QAExtractor = None
IntelligentHighlightsTask = None
QAHighlightsTask = None
HighlightsJobManager = None
HighlightsScorer = None
VideoHighlightsDetector = None
IFrameExtractor = None
QuestionIdentifier = None

def __getattr__(name):
    """Lazy loading of module attributes"""
    if name in __all__:
        _lazy_import()
        return globals()[name]
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    # Core extractors
    'IntelligentExtractor',
    'QAExtractor',

    # Pipeline tasks
    'IntelligentHighlightsTask',
    'QAHighlightsTask',

    # Job management
    'HighlightsJobManager',

    # Utilities
    'HighlightsScorer',
    'VideoHighlightsDetector',
    'IFrameExtractor',
    'QuestionIdentifier'
]
