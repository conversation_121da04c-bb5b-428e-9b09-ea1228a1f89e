#!/usr/bin/env python3
"""
Data models for highlights extraction jobs
"""

import json
import time
from enum import Enum
from dataclasses import dataclass, asdict
from typing import Dict, Any, List, Optional
from pathlib import Path


class JobStatus(Enum):
    """Status of a highlights extraction job"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class HighlightsJobInput:
    """Input data for a highlights extraction job"""
    transcription_data: Dict[str, Any]
    video_path: str
    keywords: List[str]
    target_length: int = 75
    output_format: str = "mp4"
    extract_qa_pairs: bool = True
    extract_segments: bool = True
    params: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HighlightsJobInput':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class HighlightsJobResult:
    """Result data from a highlights extraction job"""
    job_id: str
    status: JobStatus
    highlights: List[Dict[str, Any]]
    qa_pairs: List[Dict[str, Any]]
    segments: List[Dict[str, Any]]
    output_files: Dict[str, str]
    execution_time: float
    total_duration: float
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['status'] = self.status.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HighlightsJobResult':
        """Create from dictionary"""
        data['status'] = JobStatus(data['status'])
        return cls(**data)


@dataclass
class HighlightsJob:
    """Complete highlights extraction job"""
    job_id: str
    input_data: HighlightsJobInput
    status: JobStatus
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[HighlightsJobResult] = None
    progress: float = 0.0
    current_step: str = ""
    error_message: Optional[str] = None

    def __post_init__(self):
        """Post-initialization setup"""
        if self.created_at == 0:
            self.created_at = time.time()

    @property
    def duration(self) -> Optional[float]:
        """Get job execution duration"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None

    @property
    def is_finished(self) -> bool:
        """Check if job is in a finished state"""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]

    def update_progress(self, progress: float, step: str = ""):
        """Update job progress"""
        self.progress = max(0.0, min(100.0, progress))
        if step:
            self.current_step = step

    def start(self):
        """Mark job as started"""
        self.status = JobStatus.RUNNING
        self.started_at = time.time()
        self.progress = 0.0

    def complete(self, result: HighlightsJobResult):
        """Mark job as completed"""
        self.status = JobStatus.COMPLETED
        self.completed_at = time.time()
        self.result = result
        self.progress = 100.0
        self.current_step = "Completed"

    def fail(self, error_message: str):
        """Mark job as failed"""
        self.status = JobStatus.FAILED
        self.completed_at = time.time()
        self.error_message = error_message
        self.current_step = "Failed"

    def cancel(self):
        """Mark job as cancelled"""
        self.status = JobStatus.CANCELLED
        self.completed_at = time.time()
        self.current_step = "Cancelled"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['status'] = self.status.value
        if self.result:
            result['result'] = self.result.to_dict()
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HighlightsJob':
        """Create from dictionary"""
        data['status'] = JobStatus(data['status'])
        data['input_data'] = HighlightsJobInput.from_dict(data['input_data'])
        if data.get('result'):
            data['result'] = HighlightsJobResult.from_dict(data['result'])
        return cls(**data)

    def save_to_file(self, file_path: Path):
        """Save job to JSON file"""
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def load_from_file(cls, file_path: Path) -> 'HighlightsJob':
        """Load job from JSON file"""
        with open(file_path, 'r') as f:
            data = json.load(f)
        return cls.from_dict(data)
