#!/usr/bin/env python3
"""
Question-Answer Extractor for Video Highlights

This module implements the core Q&A extraction logic for identifying and extracting
engaging question-answer pairs from video highlights.
"""

import re
import logging
from typing import List, Dict, Any, Optional

# Try to import KeyBERT for automatic keyword extraction
try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except ImportError:
    KEYBERT_AVAILABLE = False


class QAExtractor:
    """
    Core Q&A extraction engine for video highlights

    Identifies and extracts engaging question-answer pairs from video content
    for social media optimization.
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize KeyBERT for automatic keyword extraction
        self.keybert_model = None
        if KEYBERT_AVAILABLE:
            try:
                self.keybert_model = KeyBERT()
                self.logger.info("KeyBERT model initialized for automatic keyword extraction")
            except Exception as e:
                self.logger.warning(f"Failed to initialize KeyBERT: {e}")
        else:
            self.logger.warning("KeyBERT not available - using fallback keyword extraction")

        self.logger.info("QAExtractor initialized")

    def extract(self, highlights: List[Dict[str, Any]], transcript_segments: List[Dict[str, Any]],
                output_dir: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract question-answer pairs from highlights

        Args:
            highlights: List of highlight segments
            transcript_segments: Full transcript segments
            output_dir: Output directory for results
            params: Additional parameters

        Returns:
            Extraction result with Q&A pairs and metadata
        """
        params = params or {}

        self.logger.info(f"Starting Q&A extraction from {len(highlights)} highlights")

        # Extract content keywords automatically from the full transcript
        content_keywords = self._extract_content_keywords(transcript_segments)
        self.logger.info(f"Extracted content keywords: {content_keywords}")

        # Extract question-answer pairs for each highlight
        qa_highlights = []
        for i, highlight in enumerate(highlights):
            qa_pair = self._extract_qa_from_highlight(
                highlight, transcript_segments, i + 1, content_keywords
            )
            if qa_pair:
                qa_highlights.append(qa_pair)

        # Generate clip timing data
        clip_windows = self._generate_clip_windows(qa_highlights)

        # Calculate extraction rate
        extraction_rate = len(qa_highlights) / len(highlights) if highlights else 0

        self.logger.info(f"Extracted {len(qa_highlights)} Q&A pairs from {len(highlights)} highlights")

        return {
            'qa_highlights': qa_highlights,
            'clip_windows': clip_windows,
            'extraction_rate': extraction_rate,
            'content_keywords': content_keywords,
            'total_highlights': len(highlights),
            'successful_extractions': len(qa_highlights)
        }

    def _extract_content_keywords(self, transcript_segments: List[Dict]) -> List[str]:
        """
        Extract relevant keywords from the full transcript using KeyBERT

        Args:
            transcript_segments: List of transcript segments

        Returns:
            List of extracted keywords relevant to the content
        """
        # Combine all transcript text
        full_transcript = ' '.join(segment.get('text', '') for segment in transcript_segments)

        if not full_transcript.strip():
            return []

        # Use KeyBERT if available
        if self.keybert_model:
            try:
                # Extract top keywords using KeyBERT
                keywords = self.keybert_model.extract_keywords(
                    full_transcript,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english'
                )[:15]  # Take top 15 keywords

                # Extract just the keyword strings
                extracted_keywords = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in keywords]
                self.logger.info(f"KeyBERT extracted content keywords: {extracted_keywords}")
                return extracted_keywords

            except Exception as e:
                self.logger.warning(f"KeyBERT extraction failed: {e}")

        # Fallback: simple keyword extraction based on common patterns
        fallback_keywords = self._extract_keywords_fallback(full_transcript)
        self.logger.info(f"Using fallback content keywords: {fallback_keywords}")
        return fallback_keywords

    def _extract_keywords_fallback(self, text: str) -> List[str]:
        """
        Fallback keyword extraction when KeyBERT is not available

        Args:
            text: Full transcript text

        Returns:
            List of extracted keywords
        """
        # Common topic categories and their keywords
        topic_keywords = {
            'relationships': ['relationship', 'love', 'dating', 'partner', 'marriage', 'trust', 'connection'],
            'business': ['business', 'company', 'startup', 'entrepreneur', 'investment', 'money', 'career'],
            'psychology': ['psychology', 'mind', 'behavior', 'emotion', 'mental', 'thinking', 'decision'],
            'philosophy': ['philosophy', 'meaning', 'purpose', 'life', 'existence', 'wisdom', 'truth'],
            'technology': ['technology', 'ai', 'artificial intelligence', 'robot', 'machine', 'computer'],
            'science': ['science', 'research', 'study', 'experiment', 'data', 'evidence', 'theory'],
            'health': ['health', 'fitness', 'nutrition', 'exercise', 'wellness', 'medical', 'body'],
            'education': ['education', 'learning', 'knowledge', 'skill', 'teaching', 'university', 'school']
        }

        text_lower = text.lower()
        found_keywords = []

        # Find keywords that appear in the text with frequency weighting
        keyword_counts = {}
        for category, keywords in topic_keywords.items():
            for keyword in keywords:
                count = text_lower.count(keyword)
                if count > 0:
                    keyword_counts[keyword] = count

        # Sort by frequency and take top keywords
        sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
        found_keywords = [kw[0] for kw in sorted_keywords[:12]]  # Take top 12 keywords

        return found_keywords

    def _extract_qa_from_highlight(self, highlight: Dict[str, Any],
                                   transcript_segments: List[Dict], clip_number: int,
                                   content_keywords: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Extract question-answer pair from a single highlight clip

        Args:
            highlight: Highlight data with timing and text
            transcript_segments: Full transcript segments
            clip_number: Sequential clip number
            content_keywords: List of content keywords

        Returns:
            QA highlight data or None if no suitable QA pair found
        """
        start_time = highlight['start_time']
        end_time = highlight['end_time']

        # Get transcript segments within the highlight timeframe
        clip_segments = self._get_segments_in_timeframe(transcript_segments, start_time, end_time)

        if not clip_segments:
            return None

        # Combine text from all segments in the clip
        full_text = ' '.join(segment.get('text', '') for segment in clip_segments)

        # Try to identify question-answer patterns
        qa_pair = self._identify_qa_pattern(full_text, clip_segments, start_time, end_time, content_keywords)

        if not qa_pair:
            return None

        # Generate engaging content for social media with content keywords
        social_content = self._generate_social_media_content(qa_pair, highlight, clip_number, content_keywords)

        return {
            'clip_number': clip_number,
            'start_time': start_time,
            'end_time': end_time,
            'duration': highlight['duration'],
            'question': qa_pair['question'],
            'answer': qa_pair['answer'],
            'question_start': qa_pair['question_start'],
            'question_end': qa_pair['question_end'],
            'answer_start': qa_pair['answer_start'],
            'answer_end': qa_pair['answer_end'],
            'full_text': full_text,
            'engagement_score': highlight.get('composite_score', highlight.get('score', 0)),
            'social_media': social_content,
            'original_highlight': highlight
        }

    def _get_segments_in_timeframe(self, transcript_segments: List[Dict],
                                   start_time: float, end_time: float) -> List[Dict]:
        """Get transcript segments that fall within the specified timeframe"""
        clip_segments = []

        for segment in transcript_segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)

            # Check if segment overlaps with the highlight timeframe
            if (seg_start < end_time and seg_end > start_time):
                clip_segments.append(segment)

        return clip_segments

    def _identify_qa_pattern(self, full_text: str, segments: List[Dict],
                            start_time: float, end_time: float, content_keywords: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Identify question-answer patterns in the text

        Args:
            full_text: Combined text from all segments
            segments: Individual transcript segments
            start_time: Clip start time
            end_time: Clip end time
            content_keywords: List of content keywords

        Returns:
            QA pattern data or None if no pattern found
        """
        # Enhanced question patterns for better detection
        question_patterns = [
            r'\b(?:what|how|why|when|where|who|which)\s+[^.!?]*\?',  # Question words with content
            r'\b(?:can|could|would|will|should|do|does|did|is|are|was|were)\s+[^.!?]*\?',  # Auxiliary verbs
            r'\b(?:tell me|explain|describe|elaborate)\s+[^.!?]*[.!?]',  # Request patterns
            r'\b(?:what do you think|what are your thoughts|how do you feel)\s+[^.!?]*[.!?]',  # Opinion questions
            r'[^.!?]*\b(?:what|how|why)\s+[^.!?]*\?',  # Questions anywhere in text
        ]

        # Find questions in the text
        questions = []
        for pattern in question_patterns:
            matches = re.finditer(pattern, full_text, re.IGNORECASE)
            for match in matches:
                question_text = match.group().strip()

                # Clean up question text
                if question_text.startswith('.') or question_text.startswith('!'):
                    question_text = question_text[1:].strip()

                # Filter for meaningful questions
                if (len(question_text) > 15 and  # Longer questions
                    question_text.count(' ') >= 3 and  # At least 4 words
                    any(word in question_text.lower() for word in ['what', 'how', 'why', 'when', 'where', 'who', 'which'])):
                    questions.append({
                        'text': question_text,
                        'start_pos': match.start(),
                        'end_pos': match.end()
                    })

        if not questions:
            return None

        # Find the best question (prefer questions with content keywords)
        best_question = self._select_best_question(questions, full_text, content_keywords)

        if not best_question:
            return None

        # Find the answer that follows the question
        answer = self._extract_answer_for_question(best_question, full_text, segments)

        if not answer:
            return None

        # Calculate timing for question and answer
        question_timing = self._calculate_text_timing(best_question['text'], segments, start_time)
        answer_timing = self._calculate_text_timing(answer, segments, start_time)

        return {
            'question': best_question['text'],
            'answer': answer,
            'question_start': question_timing['start'],
            'question_end': question_timing['end'],
            'answer_start': answer_timing['start'],
            'answer_end': answer_timing['end']
        }

    def _select_best_question(self, questions: List[Dict], full_text: str, content_keywords: List[str] = None) -> Optional[Dict]:
        """Select the most engaging question from candidates using content-aware scoring"""
        if not questions:
            return None

        # Score questions based on engagement factors
        scored_questions = []

        for question in questions:
            score = 0
            q_text = question['text'].lower()

            # Prefer questions with content-relevant keywords (dynamic)
            if content_keywords:
                for keyword in content_keywords:
                    if keyword.lower() in q_text:
                        score += 2

            # Prefer certain question types
            if any(word in q_text for word in ['what', 'how', 'why']):
                score += 1

            # Prefer questions that create curiosity
            curiosity_words = ['think', 'believe', 'feel', 'opinion', 'predict', 'expect']
            if any(word in q_text for word in curiosity_words):
                score += 1

            # Prefer questions of appropriate length (not too short, not too long)
            if 20 <= len(question['text']) <= 150:
                score += 1

            scored_questions.append((question, score))

        # Return the highest scoring question
        scored_questions.sort(key=lambda x: x[1], reverse=True)
        return scored_questions[0][0] if scored_questions[0][1] > 0 else questions[0]

    def _extract_answer_for_question(self, question: Dict, full_text: str, segments: List[Dict]) -> Optional[str]:
        """Extract the answer that follows the identified question"""
        question_end_pos = question['end_pos']

        # Look for answer in the text following the question
        remaining_text = full_text[question_end_pos:].strip()

        if not remaining_text:
            return None

        # Split into sentences and take the first few that form a coherent answer
        sentences = re.split(r'[.!?]+', remaining_text)

        answer_sentences = []
        answer_length = 0
        max_answer_length = 300  # Maximum answer length in characters

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Stop if we've reached a good answer length
            if answer_length + len(sentence) > max_answer_length and answer_sentences:
                break

            answer_sentences.append(sentence)
            answer_length += len(sentence)

            # Stop if we have a complete thought (at least 2 sentences or one long sentence)
            if len(answer_sentences) >= 2 or len(sentence) > 50:
                break

        if not answer_sentences:
            return None

        answer = '. '.join(answer_sentences)
        if not answer.endswith('.'):
            answer += '.'

        return answer

    def _calculate_text_timing(self, text: str, segments: List[Dict], clip_start: float) -> Dict[str, float]:
        """Calculate approximate timing for a piece of text within the segments"""
        # This is a simplified approach - in practice, you might want more sophisticated timing
        # For now, we'll estimate based on text position and segment timing

        if not segments:
            return {'start': clip_start, 'end': clip_start + 5.0}

        # Find the segment that likely contains this text
        for segment in segments:
            if text.lower() in segment.get('text', '').lower():
                return {
                    'start': segment.get('start', clip_start),
                    'end': segment.get('end', clip_start + 5.0)
                }

        # Fallback: use first segment timing
        first_segment = segments[0]
        return {
            'start': first_segment.get('start', clip_start),
            'end': first_segment.get('end', clip_start + 5.0)
        }

    def _generate_social_media_content(self, qa_pair: Dict[str, Any],
                                       highlight: Dict[str, Any], clip_number: int,
                                       content_keywords: List[str] = None) -> Dict[str, Any]:
        """
        Generate engaging social media content for the QA highlight

        Args:
            qa_pair: Question-answer pair data
            highlight: Original highlight data
            clip_number: Sequential clip number
            content_keywords: List of content keywords

        Returns:
            Social media optimized content
        """
        question = qa_pair['question']
        answer = qa_pair['answer']

        # Generate engaging title
        title = self._generate_engaging_title(question, answer, clip_number)

        # Generate description
        description = self._generate_description(question, answer, content_keywords)

        # Generate hashtags
        hashtags = self._generate_hashtags(question, answer, content_keywords)

        return {
            'title': title,
            'description': description,
            'hashtags': hashtags,
            'format': 'vertical_9_16',
            'duration': highlight.get('duration', 0),
            'engagement_score': highlight.get('composite_score', highlight.get('score', 0))
        }

    def _generate_engaging_title(self, question: str, answer: str, clip_number: int) -> str:
        """Generate an engaging title for the QA clip"""
        # Simplify the question for the title
        question_clean = question.replace('?', '').strip()
        if len(question_clean) > 60:
            question_clean = question_clean[:57] + "..."

        return f"Q&A #{clip_number}: {question_clean}"

    def _generate_description(self, question: str, answer: str, content_keywords: List[str] = None) -> str:
        """Generate a description for the QA clip"""
        # Create a concise description
        description = f"Q: {question}\n\nA: {answer[:200]}..."

        if content_keywords:
            description += f"\n\nTopics: {', '.join(content_keywords[:5])}"

        return description

    def _generate_hashtags(self, question: str, answer: str, content_keywords: List[str] = None) -> List[str]:
        """Generate relevant hashtags for the QA clip"""
        hashtags = ['#QA', '#Question', '#Answer', '#Highlights']

        if content_keywords:
            for keyword in content_keywords[:3]:
                hashtag = f"#{keyword.replace(' ', '').title()}"
                if hashtag not in hashtags:
                    hashtags.append(hashtag)

        return hashtags

    def _generate_clip_windows(self, qa_highlights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate clip timing data for the clip renderer"""
        clip_windows = []

        for qa in qa_highlights:
            clip_windows.append({
                'clip_number': qa['clip_number'],
                'start_time': qa['start_time'],
                'end_time': qa['end_time'],
                'duration': qa['duration'],
                'type': 'qa_highlight',
                'metadata': {
                    'question': qa['question'],
                    'answer': qa['answer'],
                    'engagement_score': qa['engagement_score']
                }
            })

        return clip_windows