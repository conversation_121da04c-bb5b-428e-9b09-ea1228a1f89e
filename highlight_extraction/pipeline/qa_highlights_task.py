#!/usr/bin/env python3
"""
Question-Answer Highlights Extractor Task

Pipeline task wrapper for extracting engaging question-answer pairs
from intelligent highlights for short-form vertical video content.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

import sys
import os
# Add the project root to the path to import BaseTask
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

# Import the core QA extractor (to be implemented)
from ..core.qa_extractor import QAExtractor


class QAHighlightsTask(BaseTask):
    """
    Pipeline task wrapper for QA highlights extraction

    This task processes intelligent highlights to extract engaging question-answer pairs
    for short-form vertical video content optimized for social media platforms.
    """

    task_name = "qa_highlights_extractor"
    requires_gpu = False

    def __init__(self):
        super().__init__()
        # Initialize the core QA extractor
        self.qa_extractor = QAExtractor()

    def run(self, job_id: str, intelligent_highlights_result: Dict[str, Any],
            transcription_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Extract question-answer pairs from intelligent highlights

        Args:
            job_id: Unique job identifier
            intelligent_highlights_result: Result from intelligent highlights extractor
            transcription_result: Result from transcription engine
            params: Additional parameters

        Returns:
            Dictionary containing QA highlights results
        """
        params = params or {}
        start_time = time.time()

        self.logger.info(f"Starting QA highlights extraction for job {job_id}")

        try:
            # Create output directory
            job_output_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            qa_highlights_dir = os.path.join(job_output_dir, "qa_highlights")
            os.makedirs(qa_highlights_dir, exist_ok=True)

            # Load highlights and transcript data
            highlights, transcript_segments = self._load_data(
                job_id, intelligent_highlights_result, transcription_result
            )

            # Use the core QA extractor to perform the actual extraction
            extraction_result = self.qa_extractor.extract(
                highlights=highlights,
                transcript_segments=transcript_segments,
                output_dir=qa_highlights_dir,
                params=params
            )

            # Generate output files
            output_files = self._generate_qa_outputs(
                extraction_result['qa_highlights'],
                qa_highlights_dir,
                job_id
            )

            execution_time = time.time() - start_time

            result = {
                'status': 'completed',
                'qa_highlights_count': len(extraction_result['qa_highlights']),
                'qa_highlights_path': output_files['qa_highlights_json'],
                'social_media_ready_path': output_files['social_media_json'],
                'clip_windows': extraction_result.get('clip_windows', []),
                'execution_time': execution_time,
                'metadata': {
                    'original_highlights_count': len(highlights),
                    'qa_extraction_rate': extraction_result.get('extraction_rate', 0),
                    'target_keywords': params.get('keywords', []),
                    'clip_windows_count': len(extraction_result.get('clip_windows', []))
                }
            }

            # Save state
            self.save_state(job_id, result, self.task_name)

            self.logger.info(f"QA highlights extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Generated {len(extraction_result['qa_highlights'])} QA highlight clips")

            return result

        except Exception as e:
            self.logger.error(f"QA highlights extraction failed: {str(e)}")
            error_result = {
                'status': 'failed',
                'error': str(e),
                'execution_time': time.time() - start_time
            }
            self.save_state(job_id, error_result, self.task_name)
            return error_result

    def _load_data(self, job_id: str, intelligent_highlights_result: Dict[str, Any],
                   transcription_result: Dict[str, Any]) -> tuple[List[Dict], List[Dict]]:
        """Load highlights and transcript data"""

        # Load highlights
        highlights_path = intelligent_highlights_result.get('highlights_path')
        if not highlights_path or not os.path.exists(highlights_path):
            raise FileNotFoundError(f"Highlights file not found: {highlights_path}")

        with open(highlights_path, 'r') as f:
            highlights_data = json.load(f)

        # Handle both old format (direct list) and new advanced format (nested under 'highlights' key)
        if isinstance(highlights_data, list):
            # Old format: direct list of highlights
            highlights = highlights_data
        elif isinstance(highlights_data, dict) and 'highlights' in highlights_data:
            # New advanced format: highlights nested under 'highlights' key
            highlights = highlights_data['highlights']
            self.logger.info(f"Loaded advanced highlights using {highlights_data.get('algorithm', 'unknown')} algorithm")
        else:
            raise ValueError(f"Invalid highlights format in {highlights_path}")

        # Load transcript
        transcript_path = transcription_result.get('transcript_path')
        if not transcript_path or not os.path.exists(transcript_path):
            raise FileNotFoundError(f"Transcript file not found: {transcript_path}")

        with open(transcript_path, 'r') as f:
            transcript_data = json.load(f)

        transcript_segments = transcript_data.get('segments', [])

        self.logger.info(f"Loaded {len(highlights)} highlights and {len(transcript_segments)} transcript segments")

        return highlights, transcript_segments

    def _generate_qa_outputs(self, qa_highlights: List[Dict[str, Any]],
                            qa_highlights_dir: str, job_id: str) -> Dict[str, str]:
        """
        Generate output files for QA highlights

        Args:
            qa_highlights: List of QA highlight data
            qa_highlights_dir: Output directory
            job_id: Job identifier

        Returns:
            Dictionary with output file paths
        """
        # Generate QA highlights JSON
        qa_highlights_json_path = os.path.join(qa_highlights_dir, f"{job_id}_qa_highlights.json")
        with open(qa_highlights_json_path, 'w') as f:
            json.dump({
                'qa_highlights': qa_highlights,
                'count': len(qa_highlights),
                'generated_at': time.time()
            }, f, indent=2)

        # Generate social media ready JSON
        social_media_data = []
        for qa in qa_highlights:
            social_media_data.append({
                'clip_number': qa.get('clip_number'),
                'question': qa.get('question'),
                'answer': qa.get('answer'),
                'duration': qa.get('duration'),
                'start_time': qa.get('start_time'),
                'end_time': qa.get('end_time'),
                'social_media': qa.get('social_media', {}),
                'engagement_score': qa.get('engagement_score', 0)
            })

        social_media_json_path = os.path.join(qa_highlights_dir, f"{job_id}_social_media.json")
        with open(social_media_json_path, 'w') as f:
            json.dump({
                'social_media_clips': social_media_data,
                'count': len(social_media_data),
                'generated_at': time.time()
            }, f, indent=2)

        return {
            'qa_highlights_json': qa_highlights_json_path,
            'social_media_json': social_media_json_path
        }
