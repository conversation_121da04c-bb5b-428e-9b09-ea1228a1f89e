#!/usr/bin/env python3
"""
Install script for advanced face detection solution

This script installs the optimal face detection backends in order of preference:
1. InsightFace SCRFD (high accuracy)
2. MediaPipe (fast and reliable)
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name, description=""):
    """Install a package using pip"""
    try:
        logger.info(f"Installing {package_name} {description}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        logger.info(f"✅ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False

def check_package(package_name):
    """Check if a package is already installed"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Install the advanced face detection packages"""
    logger.info("🚀 Installing Advanced Face Detection Solution")
    logger.info("=" * 60)

    # Core dependencies
    core_packages = [
        ("opencv-python>=4.5.0", "OpenCV for computer vision"),
        ("numpy>=1.21.0", "NumPy for numerical operations"),
    ]

    # Face detection packages in order of preference
    face_detection_packages = [
        ("insightface>=0.7.3", "InsightFace SCRFD - High accuracy"),
        ("onnxruntime>=1.15.0", "ONNX Runtime for InsightFace"),
        ("mediapipe>=0.10.0", "Google MediaPipe - Fast and reliable"),
    ]

    # Install core packages
    logger.info("📦 Installing core dependencies...")
    for package, description in core_packages:
        install_package(package, f"({description})")

    logger.info("\n🎯 Installing face detection packages...")

    # Install face detection packages
    success_count = 0
    for package, description in face_detection_packages:
        if install_package(package, f"({description})"):
            success_count += 1

    logger.info("\n" + "=" * 60)
    logger.info(f"✅ Installation complete! {success_count}/{len(face_detection_packages)} face detection packages installed.")

    # Test installations
    logger.info("\n🧪 Testing installations...")

    test_results = {}

    # Test InsightFace
    try:
        import insightface
        test_results["InsightFace SCRFD"] = "✅ Available (High accuracy)"
    except ImportError:
        test_results["InsightFace SCRFD"] = "❌ Not available"

    # Test MediaPipe
    try:
        import mediapipe
        test_results["MediaPipe"] = "✅ Available (Fast and reliable)"
    except ImportError:
        test_results["MediaPipe"] = "❌ Not available"

    # Display test results
    logger.info("\n📊 Face Detection Backend Status:")
    for backend, status in test_results.items():
        logger.info(f"  {backend}: {status}")

    # Recommendations
    available_backends = [k for k, v in test_results.items() if "✅" in v]

    if "InsightFace SCRFD" in available_backends:
        logger.info("\n🎉 EXCELLENT! InsightFace SCRFD is available - you have high-performance face detection!")
    elif "MediaPipe" in available_backends:
        logger.info("\n👍 GOOD! MediaPipe is available - you have reliable face detection!")
    else:
        logger.warning("\n⚠️  No face detection backends available.")

    logger.info("\n🔧 Usage:")
    logger.info("  The system will automatically use the optimal available backend.")
    logger.info("  You can also specify a backend manually:")
    logger.info("    FaceDetectionEngine(backend='auto')        # Auto-select optimal")
    logger.info("    FaceDetectionEngine(backend='insightface') # Force InsightFace")
    logger.info("    FaceDetectionEngine(backend='mediapipe')   # Force MediaPipe")

if __name__ == "__main__":
    main()
