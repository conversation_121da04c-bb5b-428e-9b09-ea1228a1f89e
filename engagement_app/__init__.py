"""
Audience Engagement Detection Package

A production-ready system for detecting audience engagement from long-form audio content
using unsupervised methods. Processes OpenAI Whisper transcripts and raw audio files
to output validated JSON containing highlight segments with timestamps, engagement cues,
and confidence scores.

Integration APIs:
- EngagementDetector: High-level API for custom configurations
- QuickProcessor: Simplified API for quick processing
- quick_highlights: Convenience function for minimal setup
"""

__version__ = "1.0.0"
__author__ = "MLOps Engineering Team"

# Import high-level APIs for easy access
from .api import (
    EngagementDetector,
    QuickProcessor,
    detect_engagement,
    quick_highlights
)

# Import core pipeline components
from .pipeline import (
    DataLoader,
    TranscriptPreprocessor,
    FeatureExtractor,
    EngagementScorer,
    HighlightGenerator,
    LLMReranker,
    JSONExporter
)

# Import data models
from .pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData
from .pipeline.postprocess import HighlightSegment

__all__ = [
    # High-level APIs
    "EngagementDetector",
    "QuickProcessor", 
    "detect_engagement",
    "quick_highlights",
    
    # Core pipeline components
    "DataLoader",
    "TranscriptPreprocessor", 
    "FeatureExtractor",
    "EngagementScorer",
    "HighlightGenerator",
    "LLMReranker",
    "JSONExporter",
    
    # Data models
    "WhisperTranscript",
    "WhisperSegment", 
    "AudioData",
    "HighlightSegment"
]
