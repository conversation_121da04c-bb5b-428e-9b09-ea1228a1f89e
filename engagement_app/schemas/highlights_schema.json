{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Audience Engagement Highlights Schema", "description": "Schema for validated JSON output containing highlight segments with engagement cues", "type": "object", "required": ["episode_id", "generated_utc", "version", "segments"], "properties": {"episode_id": {"type": "string", "description": "Unique identifier for the episode"}, "generated_utc": {"type": "string", "format": "date-time", "description": "UTC timestamp when highlights were generated"}, "version": {"type": "string", "pattern": "^engage-v\\d+\\.\\d+$", "description": "Version of the engagement detection algorithm"}, "processing_metadata": {"type": "object", "properties": {"total_duration_sec": {"type": "number", "minimum": 0, "description": "Total duration of processed audio in seconds"}, "segments_analyzed": {"type": "integer", "minimum": 0, "description": "Number of transcript segments analyzed"}, "llm_reranked": {"type": "boolean", "description": "Whether LLM re-ranking was applied"}, "features_used": {"type": "array", "items": {"type": "string"}, "description": "List of features used in engagement scoring"}, "processing_time_sec": {"type": "number", "minimum": 0, "description": "Total processing time in seconds"}}}, "segments": {"type": "array", "description": "Array of highlight segments ordered by engagement score", "items": {"type": "object", "required": ["id", "start_sec", "end_sec", "duration", "cues", "confidence"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the highlight segment"}, "start_sec": {"type": "number", "minimum": 0, "description": "Start time in seconds"}, "end_sec": {"type": "number", "minimum": 0, "description": "End time in seconds"}, "duration": {"type": "number", "minimum": 0, "description": "Duration of segment in seconds"}, "cues": {"type": "array", "items": {"type": "string"}, "description": "List of engagement cues detected in this segment"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score for engagement detection"}, "transcript": {"type": "string", "description": "Transcript text for this segment"}, "engagement_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Raw engagement score before ranking"}, "feature_scores": {"type": "object", "description": "Individual feature scores for debugging", "properties": {"emotion": {"type": "number"}, "sentiment_volatility": {"type": "number"}, "rhetorical": {"type": "number"}, "topic_shift": {"type": "number"}, "keyword_salience": {"type": "number"}, "laughter": {"type": "number"}, "applause": {"type": "number"}, "prosody": {"type": "number"}, "clap_similarity": {"type": "number"}}}}}}}}