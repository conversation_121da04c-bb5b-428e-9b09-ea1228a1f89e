# Audience Engagement Detection Pipeline Configuration

# Model Settings
models:
  emotion_model: "SamLowe/roberta-base-go_emotions"
  topic_model:
    n_topics: 10
    min_topic_size: 5
    window_size: 5
  audio_model: "panns_cnn14"
  spacy_model: "en_core_web_sm"

# Feature Extraction
features:
  text:
    emotion_model: "SamLowe/roberta-base-go_emotions"
    emotion_threshold: 0.1
    sentiment_window: 3
    rhetorical_patterns:
      - "\\?"  # Questions
      - "!"    # Exclamations
      - "\\b(you|your|yours)\\b"  # Second person
      - "\\b(very|really|absolutely|extremely|incredibly)\\b"  # Intensifiers
    topic_shift_threshold: 0.3
    keyword_top_k: 10
  
  audio:
    laughter_threshold: 0.5
    applause_threshold: 0.5
    prosody_std_multiplier: 1.0
    sample_rate: 16000
    hop_length: 512
    frame_length: 2048

# Scoring Algorithm
scoring:
  weights:
    emotion: 0.30
    sentiment_delta: 0.15
    rhetorical: 0.10
    topic_shift: 0.20
    keyword_salience: 0.05
    laughter: 0.07
    applause: 0.05
    prosody: 0.05
    clap_similarity: 0.03
  
  smoothing:
    alpha: 0.4
    window_size: 3
  
  normalization:
    method: "minmax"  # Options: minmax, zscore, robust

# Highlight Generation
highlights:
  selection_threshold_std: 1.0  # Standard deviations above mean
  min_duration_sec: 30
  max_duration_sec: 180
  merge_gap_sec: 8
  boundary_extension_sentences: 1
  top_k: 10
  ranking_duration_weight: 0.3

# LLM Re-ranking
llm:
  enabled: false
  provider: "openai"  # Options: openai, google
  model: "gpt-4o"
  max_segment_duration: 45
  confidence_threshold: 0.6
  rate_limit_delay: 1.0
  max_retries: 3
  context_sentences: 2

# Processing
processing:
  batch_size: 16
  num_workers: 4
  use_gpu: true
  cache_dir: ".cache"
  intermediate_save: true
  chunk_size_sec: 300  # For large audio files
  duration_tolerance_sec: 15.0  # Increased tolerance for duration mismatch

# Output
output:
  version: "engage-v1.0"
  include_debug_info: true
  pretty_print: true
  validate_schema: true

# Logging
logging:
  level: "INFO"
  format: "structured"
  file: "logs/pipeline.log"
  max_size_mb: 100
  backup_count: 5

# Performance
performance:
  target_processing_ratio: 0.125  # 1 hour audio in 7.5 minutes
  memory_limit_gb: 8
  enable_profiling: false
