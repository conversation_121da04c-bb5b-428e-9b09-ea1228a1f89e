"""
Tests for Feature Extraction Module

Tests text and audio feature extraction including emotion detection,
sentiment analysis, rhetorical patterns, and audio processing.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from pipeline.features import TextFeatureExtractor, AudioFeatureExtractor, FeatureExtractor
from pipeline.load_data import AudioData


class TestTextFeatureExtractor:
    """Test cases for TextFeatureExtractor class."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return {
            'emotion_model': 'joeddav/distilbert-go-emotions',
            'rhetorical_patterns': [
                r'\?',  # Questions
                r'!',   # Exclamations
                r'\b(you|your)\b',  # Second person
                r'\b(very|really)\b'  # Intensifiers
            ],
            'batch_size': 2
        }
    
    @pytest.fixture
    def extractor(self, config):
        """Create text feature extractor for testing."""
        return TextFeatureExtractor(config)
    
    @pytest.fixture
    def sample_texts(self):
        """Create sample texts for testing."""
        return [
            "Hello world! This is great.",
            "Are you excited? Really amazing!",
            "This is very interesting content.",
            "I'm feeling happy and surprised today."
        ]
    
    def test_initialization(self, config):
        """Test extractor initialization."""
        extractor = TextFeatureExtractor(config)
        assert extractor.config == config
        assert extractor.emotion_model_name == config['emotion_model']
        assert len(extractor.rhetorical_patterns) == len(config['rhetorical_patterns'])
    
    @patch('transformers.pipeline')
    def test_emotion_classifier_lazy_loading(self, mock_pipeline, extractor):
        """Test lazy loading of emotion classifier."""
        mock_classifier = Mock()
        mock_pipeline.return_value = mock_classifier
        
        # First access should load the model
        classifier = extractor.emotion_classifier
        assert classifier == mock_classifier
        mock_pipeline.assert_called_once()
        
        # Second access should use cached model
        classifier2 = extractor.emotion_classifier
        assert classifier2 == mock_classifier
        assert mock_pipeline.call_count == 1
    
    @patch('nltk.download')
    @patch('nltk.sentiment.SentimentIntensityAnalyzer')
    def test_sentiment_analyzer_lazy_loading(self, mock_analyzer, mock_download, extractor):
        """Test lazy loading of sentiment analyzer."""
        mock_sia = Mock()
        mock_analyzer.return_value = mock_sia
        
        analyzer = extractor.sentiment_analyzer
        assert analyzer == mock_sia
        mock_download.assert_called_with('vader_lexicon', quiet=True)
    
    def test_extract_emotion_scores_empty(self, extractor):
        """Test emotion extraction with empty input."""
        scores = extractor.extract_emotion_scores([])
        assert len(scores) == 0
    
    @patch.object(TextFeatureExtractor, 'emotion_classifier')
    def test_extract_emotion_scores(self, mock_classifier, extractor, sample_texts):
        """Test emotion score extraction."""
        # Mock classifier response
        mock_classifier.return_value = [
            [
                {'label': 'joy', 'score': 0.8},
                {'label': 'anger', 'score': 0.1},
                {'label': 'neutral', 'score': 0.1}
            ],
            [
                {'label': 'surprise', 'score': 0.7},
                {'label': 'excitement', 'score': 0.2},
                {'label': 'neutral', 'score': 0.1}
            ]
        ]
        
        scores = extractor.extract_emotion_scores(sample_texts[:2])
        
        assert len(scores) == 2
        assert scores[0] == 0.9  # joy + anger = 0.8 + 0.1
        assert scores[1] == 0.9  # surprise + excitement = 0.7 + 0.2
    
    @patch.object(TextFeatureExtractor, 'emotion_classifier')
    def test_extract_emotion_scores_error_handling(self, mock_classifier, extractor, sample_texts):
        """Test emotion extraction error handling."""
        mock_classifier.side_effect = Exception("Model error")
        
        scores = extractor.extract_emotion_scores(sample_texts)
        
        # Should return zeros on error
        assert len(scores) == len(sample_texts)
        assert all(score == 0 for score in scores)
    
    @patch.object(TextFeatureExtractor, 'sentiment_analyzer')
    def test_extract_sentiment_volatility(self, mock_analyzer, extractor, sample_texts):
        """Test sentiment volatility calculation."""
        # Mock sentiment scores
        mock_analyzer.polarity_scores.side_effect = [
            {'compound': 0.5},
            {'compound': -0.3},
            {'compound': 0.8},
            {'compound': 0.1}
        ]
        
        volatility = extractor.extract_sentiment_volatility(sample_texts)
        
        assert len(volatility) == len(sample_texts)
        assert volatility[0] == 0  # First sentence has no previous context
        assert volatility[1] > 0   # Should have some volatility
    
    def test_extract_sentiment_volatility_short_input(self, extractor):
        """Test sentiment volatility with short input."""
        volatility = extractor.extract_sentiment_volatility(["single text"])
        assert len(volatility) == 1
        assert volatility[0] == 0
    
    def test_extract_rhetorical_features(self, extractor, sample_texts):
        """Test rhetorical feature extraction."""
        scores = extractor.extract_rhetorical_features(sample_texts)
        
        assert len(scores) == len(sample_texts)
        
        # Check specific patterns
        # "Are you excited? Really amazing!" should have high score
        # (question mark, "you", exclamation, "really")
        assert scores[1] > scores[0]  # Second text has more rhetorical elements
    
    def test_extract_rhetorical_features_empty_text(self, extractor):
        """Test rhetorical extraction with empty text."""
        scores = extractor.extract_rhetorical_features(["", "test"])
        assert scores[0] == 0
        assert scores[1] >= 0
    
    @patch.object(TextFeatureExtractor, 'topic_model')
    def test_extract_topic_shifts(self, mock_topic_model, extractor, sample_texts):
        """Test topic shift detection."""
        # Mock topic model response
        mock_topic_model.fit_transform.return_value = (
            [0, 1, 0, 1],  # Topic assignments
            None  # No probabilities
        )
        
        shifts = extractor.extract_topic_shifts(sample_texts)
        
        assert len(shifts) == len(sample_texts)
        # First few should be zero (not enough context)
        assert shifts[0] == 0
    
    def test_extract_topic_shifts_short_input(self, extractor):
        """Test topic shifts with insufficient input."""
        shifts = extractor.extract_topic_shifts(["short", "input"])
        assert len(shifts) == 2
        assert all(shift == 0 for shift in shifts)
    
    @patch.object(TextFeatureExtractor, 'keyword_extractor')
    @patch.object(TextFeatureExtractor, '_tfidf_vectorizer', None)
    @patch('sklearn.feature_extraction.text.TfidfVectorizer')
    def test_extract_keyword_salience(self, mock_tfidf_class, mock_keyword_extractor, extractor, sample_texts):
        """Test keyword salience extraction."""
        # Mock TF-IDF vectorizer
        mock_vectorizer = Mock()
        mock_tfidf_matrix = Mock()
        mock_tfidf_matrix.sum.return_value = np.array([[1.0], [2.0], [1.5], [0.8]])
        mock_vectorizer.fit_transform.return_value = mock_tfidf_matrix
        mock_tfidf_class.return_value = mock_vectorizer
        
        # Mock KeyBERT extractor
        mock_keyword_extractor.extract_keywords.side_effect = [
            [('keyword1', 0.5), ('keyword2', 0.3)],
            [('keyword3', 0.7)],
            [('keyword4', 0.4), ('keyword5', 0.6)],
            []
        ]
        
        scores = extractor.extract_keyword_salience(sample_texts)
        
        assert len(scores) == len(sample_texts)
        assert all(0 <= score <= 1 for score in scores)
    
    def test_extract_keyword_salience_empty(self, extractor):
        """Test keyword salience with empty input."""
        scores = extractor.extract_keyword_salience([])
        assert len(scores) == 0
    
    def test_extract_all_text_features(self, extractor):
        """Test extraction of all text features."""
        df = pd.DataFrame({
            'text': [
                "Hello world! This is great.",
                "Are you excited? Really amazing!",
                "This is very interesting content."
            ]
        })
        
        with patch.object(extractor, 'extract_emotion_scores', return_value=np.array([0.5, 0.8, 0.3])), \
             patch.object(extractor, 'extract_sentiment_volatility', return_value=np.array([0.0, 0.6, 0.2])), \
             patch.object(extractor, 'extract_rhetorical_features', return_value=np.array([0.2, 0.9, 0.4])), \
             patch.object(extractor, 'extract_topic_shifts', return_value=np.array([0.0, 0.3, 0.1])), \
             patch.object(extractor, 'extract_keyword_salience', return_value=np.array([0.4, 0.7, 0.5])):
            
            result_df = extractor.extract_all_text_features(df)
            
            # Check that all feature columns were added
            expected_columns = [
                'emotion_score', 'sentiment_volatility', 'rhetorical_score',
                'topic_shift_score', 'keyword_salience'
            ]
            assert all(col in result_df.columns for col in expected_columns)
            assert len(result_df) == len(df)


class TestAudioFeatureExtractor:
    """Test cases for AudioFeatureExtractor class."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return {
            'sample_rate': 16000,
            'hop_length': 512,
            'frame_length': 2048,
            'prosody_std_multiplier': 1.0
        }
    
    @pytest.fixture
    def extractor(self, config):
        """Create audio feature extractor for testing."""
        return AudioFeatureExtractor(config)
    
    @pytest.fixture
    def sample_audio_data(self):
        """Create sample audio data for testing."""
        # Create 5 seconds of sample audio
        duration = 5.0
        sample_rate = 16000
        audio = np.random.randn(int(duration * sample_rate)) * 0.1
        
        return AudioData(
            audio=audio,
            sample_rate=sample_rate,
            duration=duration,
            channels=1,
            file_path="test.wav"
        )
    
    @pytest.fixture
    def sample_timestamps(self):
        """Create sample timestamps for testing."""
        return [(0.0, 2.0), (2.0, 4.0), (4.0, 5.0)]
    
    def test_initialization(self, config):
        """Test extractor initialization."""
        extractor = AudioFeatureExtractor(config)
        assert extractor.config == config
        assert extractor.sample_rate == config['sample_rate']
    
    @patch('panns_inference.AudioTagging')
    def test_audio_tagger_lazy_loading(self, mock_audio_tagging, extractor):
        """Test lazy loading of audio tagger."""
        mock_tagger = Mock()
        mock_audio_tagging.return_value = mock_tagger
        
        tagger = extractor.audio_tagger
        assert tagger == mock_tagger
        mock_audio_tagging.assert_called_once()
    
    @patch('panns_inference.AudioTagging')
    def test_audio_tagger_error_handling(self, mock_audio_tagging, extractor):
        """Test audio tagger error handling."""
        mock_audio_tagging.side_effect = Exception("Model loading failed")
        
        tagger = extractor.audio_tagger
        assert tagger is None
    
    def test_extract_laughter_applause_no_tagger(self, extractor, sample_audio_data, sample_timestamps):
        """Test laughter/applause extraction without tagger."""
        extractor._audio_tagger = None
        
        laughter, applause = extractor.extract_laughter_applause(sample_audio_data, sample_timestamps)
        
        assert len(laughter) == len(sample_timestamps)
        assert len(applause) == len(sample_timestamps)
        assert all(score == 0 for score in laughter)
        assert all(score == 0 for score in applause)
    
    @patch.object(AudioFeatureExtractor, 'audio_tagger')
    def test_extract_laughter_applause(self, mock_tagger, extractor, sample_audio_data, sample_timestamps):
        """Test laughter/applause extraction."""
        # Mock tagger response
        mock_output = np.zeros((1, 527))  # AudioSet has 527 classes
        mock_output[0, 321] = 0.8  # Laughter
        mock_output[0, 138] = 0.3  # Applause
        mock_tagger.inference.return_value = (mock_output, None)
        
        laughter, applause = extractor.extract_laughter_applause(sample_audio_data, sample_timestamps)
        
        assert len(laughter) == len(sample_timestamps)
        assert len(applause) == len(sample_timestamps)
        assert all(score >= 0 for score in laughter)
        assert all(score >= 0 for score in applause)
    
    def test_extract_prosodic_emphasis(self, extractor, sample_audio_data, sample_timestamps):
        """Test prosodic emphasis extraction."""
        scores = extractor.extract_prosodic_emphasis(sample_audio_data, sample_timestamps)
        
        assert len(scores) == len(sample_timestamps)
        assert all(score >= 0 for score in scores)
    
    def test_extract_all_audio_features(self, extractor, sample_audio_data):
        """Test extraction of all audio features."""
        df = pd.DataFrame({
            'start_sec': [0.0, 2.0, 4.0],
            'end_sec': [2.0, 4.0, 5.0]
        })
        
        with patch.object(extractor, 'extract_laughter_applause', return_value=(np.array([0.5, 0.3, 0.1]), np.array([0.2, 0.4, 0.0]))), \
             patch.object(extractor, 'extract_prosodic_emphasis', return_value=np.array([0.7, 0.5, 0.3])):
            
            result_df = extractor.extract_all_audio_features(sample_audio_data, df)
            
            # Check that all feature columns were added
            expected_columns = ['laughter_score', 'applause_score', 'prosody_score', 'clap_similarity']
            assert all(col in result_df.columns for col in expected_columns)
            assert len(result_df) == len(df)


class TestFeatureExtractor:
    """Test cases for main FeatureExtractor class."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return {
            'text': {
                'emotion_model': 'joeddav/distilbert-go-emotions',
                'rhetorical_patterns': [r'\?', r'!']
            },
            'audio': {
                'sample_rate': 16000,
                'hop_length': 512
            }
        }
    
    @pytest.fixture
    def extractor(self, config):
        """Create feature extractor for testing."""
        return FeatureExtractor(config)
    
    def test_initialization(self, config):
        """Test extractor initialization."""
        extractor = FeatureExtractor(config)
        assert isinstance(extractor.text_extractor, TextFeatureExtractor)
        assert isinstance(extractor.audio_extractor, AudioFeatureExtractor)
    
    def test_extract_features_text_only(self, extractor):
        """Test feature extraction with text only."""
        df = pd.DataFrame({
            'text': ["Hello world!", "How are you?"]
        })
        
        with patch.object(extractor.text_extractor, 'extract_all_text_features', return_value=df):
            result_df = extractor.extract_features(df)
            
            # Should add zero audio features
            audio_columns = ['laughter_score', 'applause_score', 'prosody_score', 'clap_similarity']
            assert all(col in result_df.columns for col in audio_columns)
            assert all(result_df[col].iloc[0] == 0.0 for col in audio_columns)
    
    def test_extract_features_with_audio(self, extractor):
        """Test feature extraction with audio data."""
        df = pd.DataFrame({
            'text': ["Hello world!", "How are you?"],
            'start_sec': [0.0, 2.0],
            'end_sec': [2.0, 4.0]
        })
        
        audio_data = Mock()
        
        with patch.object(extractor.text_extractor, 'extract_all_text_features', return_value=df), \
             patch.object(extractor.audio_extractor, 'extract_all_audio_features', return_value=df):
            
            result_df = extractor.extract_features(df, audio_data)
            
            # Should call both extractors
            extractor.text_extractor.extract_all_text_features.assert_called_once_with(df)
            extractor.audio_extractor.extract_all_audio_features.assert_called_once_with(audio_data, df)
