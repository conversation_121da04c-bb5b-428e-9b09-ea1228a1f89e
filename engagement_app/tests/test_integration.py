"""
Integration Tests for Engagement Detection Pipeline

Tests the complete pipeline end-to-end with realistic data
and validates output format and quality.
"""

import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch
import numpy as np
import pandas as pd

import sys
sys.path.append(str(Path(__file__).parent.parent))

from pipeline import (
    DataLoader, TranscriptPreprocessor, FeatureExtractor,
    EngagementScorer, HighlightGenerator, JSONExporter
)
from pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData


class TestPipelineIntegration:
    """Integration tests for the complete pipeline."""
    
    @pytest.fixture
    def sample_config(self):
        """Create sample configuration for testing."""
        return {
            'features': {
                'text': {
                    'emotion_model': 'joeddav/distilbert-go-emotions',
                    'rhetorical_patterns': [r'\?', r'!', r'\b(you|your)\b'],
                    'batch_size': 2
                },
                'audio': {
                    'sample_rate': 16000,
                    'hop_length': 512,
                    'frame_length': 2048
                }
            },
            'scoring': {
                'weights': {
                    'emotion': 0.30,
                    'sentiment_delta': 0.15,
                    'rhetorical': 0.10,
                    'topic_shift': 0.20,
                    'keyword_salience': 0.05,
                    'laughter': 0.07,
                    'applause': 0.05,
                    'prosody': 0.05,
                    'clap_similarity': 0.03
                },
                'smoothing': {'alpha': 0.4},
                'normalization': {'method': 'minmax'}
            },
            'highlights': {
                'selection_threshold_std': 1.0,
                'min_duration_sec': 10,  # Lower for testing
                'max_duration_sec': 60,
                'merge_gap_sec': 5,
                'top_k': 5
            },
            'output': {
                'version': 'engage-v1.0',
                'include_debug_info': True,
                'validate_schema': True
            }
        }
    
    @pytest.fixture
    def sample_transcript_data(self):
        """Create realistic transcript data for testing."""
        segments = [
            {
                "start": 0.0,
                "end": 6.5,
                "text": "Welcome to our podcast! Today we have an amazing guest."
            },
            {
                "start": 6.5,
                "end": 14.2,
                "text": "Are you excited about artificial intelligence? I absolutely am!"
            },
            {
                "start": 14.2,
                "end": 22.8,
                "text": "This technology is going to change everything we know about work."
            },
            {
                "start": 22.8,
                "end": 30.1,
                "text": "But here's the really surprising part that nobody talks about."
            },
            {
                "start": 30.1,
                "end": 38.5,
                "text": "The biggest challenge isn't technical - it's human! Can you believe that?"
            },
            {
                "start": 38.5,
                "end": 45.0,
                "text": "We need to completely rethink how we approach this problem."
            }
        ]
        
        return {
            "segments": segments,
            "language": "en",
            "duration": 45.0
        }
    
    @pytest.fixture
    def sample_audio_data(self):
        """Create synthetic audio data for testing."""
        duration = 45.0
        sample_rate = 16000
        num_samples = int(duration * sample_rate)
        
        # Create realistic audio with varying energy
        audio = np.random.randn(num_samples) * 0.1
        
        # Add some energy spikes for speech simulation
        for i in range(0, num_samples, sample_rate):
            if i + sample_rate // 2 < num_samples:
                audio[i:i + sample_rate // 2] *= np.random.uniform(2, 4)
        
        return AudioData(
            audio=audio,
            sample_rate=sample_rate,
            duration=duration,
            channels=1,
            file_path="test_audio.wav"
        )
    
    def test_complete_pipeline_text_only(self, sample_config, sample_transcript_data):
        """Test complete pipeline with text-only input."""
        # Convert to required format
        segments = [WhisperSegment(**seg) for seg in sample_transcript_data['segments']]
        transcript = WhisperTranscript(segments=segments, duration=sample_transcript_data['duration'])
        
        # Initialize components
        preprocessor = TranscriptPreprocessor()
        feature_extractor = FeatureExtractor(sample_config['features'])
        scorer = EngagementScorer(sample_config['scoring'])
        highlight_generator = HighlightGenerator(sample_config['highlights'])
        
        # Mock the heavy ML models to avoid loading them in tests
        with patch.object(feature_extractor.text_extractor, 'extract_emotion_scores', return_value=np.array([0.5, 0.8, 0.3, 0.9, 0.7, 0.4])), \
             patch.object(feature_extractor.text_extractor, 'extract_sentiment_volatility', return_value=np.array([0.0, 0.6, 0.2, 0.8, 0.5, 0.3])), \
             patch.object(feature_extractor.text_extractor, 'extract_rhetorical_features', return_value=np.array([0.2, 0.9, 0.1, 0.3, 0.8, 0.2])), \
             patch.object(feature_extractor.text_extractor, 'extract_topic_shifts', return_value=np.array([0.0, 0.3, 0.7, 0.5, 0.6, 0.2])), \
             patch.object(feature_extractor.text_extractor, 'extract_keyword_salience', return_value=np.array([0.4, 0.7, 0.5, 0.8, 0.6, 0.3])):
            
            # Step 1: Preprocess
            df = preprocessor.process_transcript(transcript, "test_episode")
            assert len(df) > 0
            assert 'text' in df.columns
            assert 'start_sec' in df.columns
            assert 'end_sec' in df.columns
            
            # Step 2: Extract features
            df_features = feature_extractor.extract_features(df)
            
            # Check that text features were added
            text_feature_columns = [
                'emotion_score', 'sentiment_volatility', 'rhetorical_score',
                'topic_shift_score', 'keyword_salience'
            ]
            assert all(col in df_features.columns for col in text_feature_columns)
            
            # Check that audio features were added as zeros
            audio_feature_columns = ['laughter_score', 'applause_score', 'prosody_score', 'clap_similarity']
            assert all(col in df_features.columns for col in audio_feature_columns)
            assert all(df_features[col].sum() == 0 for col in audio_feature_columns)
            
            # Step 3: Calculate scores
            df_scores = scorer.calculate_engagement_scores(df_features)
            assert 'engagement_score' in df_scores.columns
            assert all(0 <= score <= 1 for score in df_scores['engagement_score'])
            
            # Step 4: Generate highlights
            highlights = highlight_generator.generate_highlights(df_scores)
            
            # Validate highlights
            assert isinstance(highlights, list)
            if highlights:  # May be empty if no segments meet criteria
                for highlight in highlights:
                    assert hasattr(highlight, 'start_sec')
                    assert hasattr(highlight, 'end_sec')
                    assert hasattr(highlight, 'duration')
                    assert hasattr(highlight, 'transcript')
                    assert hasattr(highlight, 'cues')
                    assert hasattr(highlight, 'max_engagement_score')
                    assert highlight.start_sec < highlight.end_sec
                    assert highlight.duration > 0
    
    def test_complete_pipeline_with_audio(self, sample_config, sample_transcript_data, sample_audio_data):
        """Test complete pipeline with audio input."""
        # Convert to required format
        segments = [WhisperSegment(**seg) for seg in sample_transcript_data['segments']]
        transcript = WhisperTranscript(segments=segments, duration=sample_transcript_data['duration'])
        
        # Initialize components
        preprocessor = TranscriptPreprocessor()
        feature_extractor = FeatureExtractor(sample_config['features'])
        scorer = EngagementScorer(sample_config['scoring'])
        highlight_generator = HighlightGenerator(sample_config['highlights'])
        
        # Mock the heavy ML models
        with patch.object(feature_extractor.text_extractor, 'extract_emotion_scores', return_value=np.array([0.5, 0.8, 0.3, 0.9, 0.7, 0.4])), \
             patch.object(feature_extractor.text_extractor, 'extract_sentiment_volatility', return_value=np.array([0.0, 0.6, 0.2, 0.8, 0.5, 0.3])), \
             patch.object(feature_extractor.text_extractor, 'extract_rhetorical_features', return_value=np.array([0.2, 0.9, 0.1, 0.3, 0.8, 0.2])), \
             patch.object(feature_extractor.text_extractor, 'extract_topic_shifts', return_value=np.array([0.0, 0.3, 0.7, 0.5, 0.6, 0.2])), \
             patch.object(feature_extractor.text_extractor, 'extract_keyword_salience', return_value=np.array([0.4, 0.7, 0.5, 0.8, 0.6, 0.3])), \
             patch.object(feature_extractor.audio_extractor, 'extract_laughter_applause', return_value=(np.array([0.1, 0.3, 0.0, 0.7, 0.2, 0.1]), np.array([0.0, 0.1, 0.0, 0.0, 0.5, 0.0]))), \
             patch.object(feature_extractor.audio_extractor, 'extract_prosodic_emphasis', return_value=np.array([0.3, 0.8, 0.2, 0.6, 0.9, 0.4])):
            
            # Run pipeline
            df = preprocessor.process_transcript(transcript, "test_episode")
            df_features = feature_extractor.extract_features(df, sample_audio_data)
            df_scores = scorer.calculate_engagement_scores(df_features)
            highlights = highlight_generator.generate_highlights(df_scores)
            
            # Check that audio features were processed
            assert df_features['laughter_score'].sum() > 0
            assert df_features['prosody_score'].sum() > 0
    
    def test_json_export_integration(self, sample_config):
        """Test JSON export with realistic highlight data."""
        from pipeline.postprocess import HighlightSegment
        
        # Create mock highlights
        mock_sentences = [
            {'text': 'This is amazing!', 'engagement_score': 0.9, 'emotion_score': 0.8},
            {'text': 'Really incredible stuff.', 'engagement_score': 0.85, 'emotion_score': 0.7}
        ]
        
        highlights = [
            HighlightSegment(10.0, 25.0, mock_sentences),
            HighlightSegment(45.0, 60.0, mock_sentences)
        ]
        
        # Initialize exporter
        exporter = JSONExporter(sample_config['output'])
        
        # Create JSON
        processing_stats = {
            'total_duration_sec': 120.0,
            'segments_analyzed': 50,
            'processing_time_sec': 15.2
        }
        
        json_data = exporter.create_output_json("test_episode", highlights, processing_stats)
        
        # Validate structure
        assert 'episode_id' in json_data
        assert 'generated_utc' in json_data
        assert 'version' in json_data
        assert 'processing_metadata' in json_data
        assert 'segments' in json_data
        
        assert json_data['episode_id'] == "test_episode"
        assert json_data['version'] == sample_config['output']['version']
        assert len(json_data['segments']) == len(highlights)
        
        # Validate segment structure
        for segment_data in json_data['segments']:
            required_fields = ['id', 'start_sec', 'end_sec', 'duration', 'cues', 'confidence', 'transcript']
            assert all(field in segment_data for field in required_fields)
    
    def test_json_export_to_file(self, sample_config):
        """Test JSON export to file."""
        from pipeline.postprocess import HighlightSegment
        
        # Create mock highlight
        mock_sentences = [{'text': 'Test', 'engagement_score': 0.8}]
        highlights = [HighlightSegment(0.0, 10.0, mock_sentences)]
        
        # Initialize exporter
        exporter = JSONExporter(sample_config['output'])
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            processing_stats = {'total_duration_sec': 60.0, 'segments_analyzed': 10, 'processing_time_sec': 5.0}
            success = exporter.export_highlights("test", highlights, processing_stats, temp_path)
            
            assert success
            assert Path(temp_path).exists()
            
            # Validate file content
            with open(temp_path, 'r') as f:
                data = json.load(f)
            
            assert data['episode_id'] == "test"
            assert len(data['segments']) == 1
            
        finally:
            # Clean up
            Path(temp_path).unlink(missing_ok=True)
    
    def test_pipeline_error_handling(self, sample_config):
        """Test pipeline error handling with invalid data."""
        # Test with empty transcript
        empty_segments = []
        empty_transcript = WhisperTranscript(segments=empty_segments, duration=0.0)
        
        preprocessor = TranscriptPreprocessor()
        
        with pytest.raises(ValueError, match="No valid sentences found"):
            preprocessor.process_transcript(empty_transcript, "test")
    
    def test_pipeline_performance_metrics(self, sample_config, sample_transcript_data):
        """Test that pipeline generates performance metrics."""
        # Convert to required format
        segments = [WhisperSegment(**seg) for seg in sample_transcript_data['segments']]
        transcript = WhisperTranscript(segments=segments, duration=sample_transcript_data['duration'])
        
        # Initialize scorer
        scorer = EngagementScorer(sample_config['scoring'])
        
        # Create mock DataFrame with features
        df = pd.DataFrame({
            'emotion_score': [0.5, 0.8, 0.3],
            'sentiment_volatility': [0.2, 0.6, 0.1],
            'rhetorical_score': [0.3, 0.9, 0.2],
            'topic_shift_score': [0.1, 0.4, 0.7],
            'keyword_salience': [0.4, 0.7, 0.3],
            'laughter_score': [0.0, 0.5, 0.0],
            'applause_score': [0.0, 0.0, 0.0],
            'prosody_score': [0.2, 0.8, 0.3],
            'clap_similarity': [0.0, 0.0, 0.0]
        })
        
        # Calculate scores
        df_scores = scorer.calculate_engagement_scores(df)
        
        # Get statistics
        stats = scorer.get_score_statistics(df_scores)
        
        # Validate statistics
        required_stats = ['mean', 'std', 'min', 'max', 'median', 'q25', 'q75', 'count']
        assert all(stat in stats for stat in required_stats)
        assert stats['count'] == len(df)
        assert 0 <= stats['mean'] <= 1
        assert 0 <= stats['min'] <= stats['max'] <= 1
