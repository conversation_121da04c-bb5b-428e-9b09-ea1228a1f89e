"""
Tests for Preprocessing Module

Tests transcript preprocessing including sentence splitting,
timestamp interpolation, and data validation.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from pipeline.preprocess import TranscriptPreprocessor
from pipeline.load_data import WhisperSegment, WhisperTranscript


class TestTranscriptPreprocessor:
    """Test cases for TranscriptPreprocessor class."""
    
    @pytest.fixture
    def preprocessor(self):
        """Create preprocessor instance for testing."""
        return TranscriptPreprocessor()
    
    @pytest.fixture
    def sample_segment(self):
        """Create sample Whisper segment for testing."""
        return WhisperSegment(
            start=10.0,
            end=15.0,
            text="Hello world! How are you doing today? This is a test."
        )
    
    @pytest.fixture
    def sample_transcript(self):
        """Create sample Whisper transcript for testing."""
        segments = [
            WhisperSegment(start=0.0, end=5.0, text="Welcome to our podcast."),
            WhisperSegment(start=5.0, end=12.0, text="Today we're talking about AI. It's fascinating!"),
            WhisperSegment(start=12.0, end=18.0, text="What do you think? Are you excited about the future?")
        ]
        return WhisperTranscript(segments=segments, duration=18.0)
    
    def test_clean_text_basic(self, preprocessor):
        """Test basic text cleaning functionality."""
        # Test removing artifacts
        text = "Hello [MUSIC] world (inaudible) test"
        cleaned = preprocessor.clean_text(text)
        assert cleaned == "Hello world test"
        
        # Test whitespace normalization
        text = "Hello    world\n\ntest"
        cleaned = preprocessor.clean_text(text)
        assert cleaned == "Hello world test"
        
        # Test empty text
        assert preprocessor.clean_text("") == ""
        assert preprocessor.clean_text("   ") == ""
    
    def test_clean_text_edge_cases(self, preprocessor):
        """Test text cleaning edge cases."""
        # Test multiple artifacts
        text = "[APPLAUSE] Great point! [MUSIC] (unclear) Really?"
        cleaned = preprocessor.clean_text(text)
        assert cleaned == "Great point! Really?"
        
        # Test only artifacts
        text = "[MUSIC] (inaudible) [APPLAUSE]"
        cleaned = preprocessor.clean_text(text)
        assert cleaned == ""
    
    def test_split_into_sentences(self, preprocessor):
        """Test sentence splitting functionality."""
        text = "Hello world! How are you? This is great."
        sentences = preprocessor.split_into_sentences(text)
        
        assert len(sentences) == 3
        assert "Hello world!" in sentences
        assert "How are you?" in sentences
        assert "This is great." in sentences
    
    def test_split_into_sentences_edge_cases(self, preprocessor):
        """Test sentence splitting edge cases."""
        # Empty text
        assert preprocessor.split_into_sentences("") == []
        
        # Single sentence
        sentences = preprocessor.split_into_sentences("Hello world")
        assert len(sentences) == 1
        
        # Very short sentences (should be filtered)
        sentences = preprocessor.split_into_sentences("Hi. Ok. Yes. This is longer.")
        assert len(sentences) == 1  # Only "This is longer." should remain
        assert "This is longer." in sentences
    
    def test_interpolate_timestamps_single_sentence(self, preprocessor):
        """Test timestamp interpolation for single sentence."""
        segment = WhisperSegment(start=10.0, end=15.0, text="Hello world")
        sentences = ["Hello world"]
        
        timestamps = preprocessor.interpolate_timestamps(segment, sentences)
        
        assert len(timestamps) == 1
        assert timestamps[0] == (10.0, 15.0)
    
    def test_interpolate_timestamps_multiple_sentences(self, preprocessor):
        """Test timestamp interpolation for multiple sentences."""
        segment = WhisperSegment(start=0.0, end=10.0, text="Hello. World.")
        sentences = ["Hello.", "World."]  # Equal length
        
        timestamps = preprocessor.interpolate_timestamps(segment, sentences)
        
        assert len(timestamps) == 2
        assert timestamps[0][0] == 0.0
        assert timestamps[1][1] == 10.0
        assert timestamps[0][1] == timestamps[1][0]  # No gaps
    
    def test_interpolate_timestamps_proportional(self, preprocessor):
        """Test proportional timestamp interpolation."""
        segment = WhisperSegment(start=0.0, end=10.0, text="Hi. This is a much longer sentence.")
        sentences = ["Hi.", "This is a much longer sentence."]
        
        timestamps = preprocessor.interpolate_timestamps(segment, sentences)
        
        # First sentence should get less time (shorter)
        # Second sentence should get more time (longer)
        assert timestamps[0][1] - timestamps[0][0] < timestamps[1][1] - timestamps[1][0]
    
    def test_interpolate_timestamps_empty(self, preprocessor):
        """Test timestamp interpolation with empty sentences."""
        segment = WhisperSegment(start=0.0, end=10.0, text="")
        sentences = []
        
        timestamps = preprocessor.interpolate_timestamps(segment, sentences)
        assert timestamps == []
    
    def test_process_segment(self, preprocessor, sample_segment):
        """Test processing a single segment."""
        records = preprocessor.process_segment(sample_segment, 0)
        
        assert len(records) > 0
        assert all('text' in record for record in records)
        assert all('start_sec' in record for record in records)
        assert all('end_sec' in record for record in records)
        assert all('duration' in record for record in records)
        assert all(record['segment_id'] == 0 for record in records)
    
    def test_process_segment_empty_text(self, preprocessor):
        """Test processing segment with empty text."""
        segment = WhisperSegment(start=0.0, end=5.0, text="")
        records = preprocessor.process_segment(segment, 0)
        assert records == []
    
    def test_process_transcript(self, preprocessor, sample_transcript):
        """Test processing complete transcript."""
        df = preprocessor.process_transcript(sample_transcript, "test_episode")
        
        # Check DataFrame structure
        expected_columns = [
            'episode_id', 'segment_id', 'sentence_id', 'start_sec',
            'end_sec', 'duration', 'text', 'original_start', 'original_end'
        ]
        assert all(col in df.columns for col in expected_columns)
        
        # Check data integrity
        assert len(df) > 0
        assert all(df['episode_id'] == "test_episode")
        assert all(df['duration'] > 0)
        assert all(df['start_sec'] < df['end_sec'])
    
    def test_process_transcript_validation(self, preprocessor, sample_transcript):
        """Test transcript processing validation."""
        df = preprocessor.process_transcript(sample_transcript, "test_episode")
        
        # Check for negative durations
        assert all(df['duration'] > 0)
        
        # Check for empty text
        assert all(df['text'].str.strip() != '')
        
        # Check timestamp ordering within segments
        for segment_id in df['segment_id'].unique():
            segment_df = df[df['segment_id'] == segment_id].sort_values('sentence_id')
            for i in range(len(segment_df) - 1):
                current_end = segment_df.iloc[i]['end_sec']
                next_start = segment_df.iloc[i + 1]['start_sec']
                assert current_end <= next_start + 0.01  # Small tolerance
    
    def test_validate_processed_data_negative_duration(self, preprocessor):
        """Test validation catches negative durations."""
        df = pd.DataFrame({
            'episode_id': ['test'],
            'segment_id': [0],
            'sentence_id': [0],
            'start_sec': [10.0],
            'end_sec': [5.0],  # End before start
            'duration': [-5.0],
            'text': ['test']
        })
        
        with pytest.raises(ValueError, match="non-positive duration"):
            preprocessor._validate_processed_data(df)
    
    @patch('engagement_app.pipeline.preprocess.logger')
    def test_validate_processed_data_warnings(self, mock_logger, preprocessor):
        """Test validation warnings for edge cases."""
        df = pd.DataFrame({
            'episode_id': ['test', 'test'],
            'segment_id': [0, 0],
            'sentence_id': [0, 1],
            'start_sec': [0.0, 2.0],
            'end_sec': [3.0, 4.0],  # Overlapping timestamps
            'duration': [3.0, 2.0],
            'text': ['test', '']  # Empty text
        })
        
        # Should not raise exception but log warnings
        preprocessor._validate_processed_data(df)
        
        # Check that warnings were logged
        assert mock_logger.warning.called
    
    def test_spacy_model_fallback(self):
        """Test fallback when spaCy model is not available."""
        with patch('spacy.load', side_effect=OSError("Model not found")):
            preprocessor = TranscriptPreprocessor("nonexistent_model")
            
            # Should still work with basic English model
            text = "Hello world! How are you?"
            sentences = preprocessor.split_into_sentences(text)
            assert len(sentences) > 0
