"""
Sample Data Integration Tests

Tests the complete engagement detection pipeline using real sample data
from the <PERSON><PERSON><PERSON> podcast interview. This validates that the pipeline
works correctly with actual Whisper transcripts and audio files.

This test suite uses the actual sample data located in:
- sample/audio1/transcript/transcript.json (Whisper transcript)
- sample/audio1/transcript/audio.mp3 (Audio file)

The sample contains a ~61-minute interview with physicist <PERSON><PERSON><PERSON>
discussing AI, physics, and the future - perfect for engagement detection.
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import patch, Mock

# Handle optional dependencies gracefully
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Create minimal pytest-like decorators for standalone testing
    class pytest:
        @staticmethod
        def fixture(func):
            return func

        @staticmethod
        def skip(reason):
            def decorator(func):
                def wrapper(*args, **kwargs):
                    print(f"SKIPPED: {reason}")
                    return True
                return wrapper
            return decorator

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    # Create minimal numpy-like interface for testing
    class np:
        @staticmethod
        def random():
            import random
            class RandomModule:
                @staticmethod
                def uniform(low, high, size):
                    return [random.uniform(low, high) for _ in range(size)]

                @staticmethod
                def beta(a, b, size):
                    return [random.random() for _ in range(size)]

                @staticmethod
                def exponential(scale, size):
                    return [random.expovariate(1/scale) for _ in range(size)]

                @staticmethod
                def gamma(shape, scale, size):
                    return [random.gammavariate(shape, scale) for _ in range(size)]

                @staticmethod
                def choice(arr, size, replace=True):
                    import random
                    return [random.choice(arr) for _ in range(size)]

            return RandomModule()

        @staticmethod
        def zeros(size):
            return [0.0] * size

        @staticmethod
        def array(data):
            return data

        @staticmethod
        def array_equal(a, b):
            return a == b

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    # Create minimal pandas-like interface
    class pd:
        class DataFrame:
            def __init__(self, data):
                self.data = data
                self.columns = list(data.keys()) if isinstance(data, dict) else []

            def __len__(self):
                if self.data and isinstance(self.data, dict):
                    return len(next(iter(self.data.values())))
                return 0

            def __getitem__(self, key):
                if isinstance(self.data, dict):
                    return self.data.get(key, [])
                return []

import sys
sys.path.append(str(Path(__file__).parent.parent))

# Import pipeline modules with error handling
try:
    from pipeline import (
        DataLoader, TranscriptPreprocessor, FeatureExtractor,
        EngagementScorer, HighlightGenerator, JSONExporter
    )
    from pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData
    PIPELINE_AVAILABLE = True
except ImportError as e:
    PIPELINE_AVAILABLE = False
    print(f"Warning: Pipeline modules not available: {e}")

    # Create mock classes for testing structure
    class DataLoader:
        def load_transcript(self, path): pass
        def load_audio(self, path): pass

    class TranscriptPreprocessor:
        def process_transcript(self, transcript, episode_id): pass


def skip_if_missing_deps(dependencies):
    """Decorator to skip tests if required dependencies are missing."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            missing = []
            for dep_name, available in dependencies.items():
                if not available:
                    missing.append(dep_name)

            if missing:
                print(f"SKIPPED {func.__name__}: Missing dependencies: {', '.join(missing)}")
                return True

            return func(*args, **kwargs)
        return wrapper
    return decorator


class TestSampleDataPipeline:
    """Integration tests using real sample data from Michio Kaku interview."""
    
    @pytest.fixture
    def sample_data_paths(self):
        """Get paths to sample data files."""
        # Paths relative to the test file location
        base_path = Path(__file__).parent.parent.parent / "sample" / "audio1" / "transcript"
        
        return {
            'transcript_json': base_path / "transcript.json",
            'audio_mp3': base_path / "audio.mp3",
            'transcript_txt': base_path / "transcript.txt",
            'transcript_vtt': base_path / "transcript.vtt"
        }
    
    @pytest.fixture
    def pipeline_config(self):
        """Create realistic pipeline configuration for testing."""
        return {
            'features': {
                'text': {
                    'emotion_model': 'joeddav/distilbert-go-emotions',
                    'rhetorical_patterns': [
                        r'\?',  # Questions
                        r'!',   # Exclamations  
                        r'\b(you|your|yours)\b',  # Second person
                        r'\b(very|really|absolutely|extremely|incredibly)\b'  # Intensifiers
                    ],
                    'batch_size': 4  # Smaller batch for testing
                },
                'audio': {
                    'sample_rate': 16000,
                    'hop_length': 512,
                    'frame_length': 2048,
                    'prosody_std_multiplier': 1.0
                }
            },
            'scoring': {
                'weights': {
                    'emotion': 0.30,
                    'sentiment_delta': 0.15,
                    'rhetorical': 0.10,
                    'topic_shift': 0.20,
                    'keyword_salience': 0.05,
                    'laughter': 0.07,
                    'applause': 0.05,
                    'prosody': 0.05,
                    'clap_similarity': 0.03
                },
                'smoothing': {'alpha': 0.4, 'window_size': 3},
                'normalization': {'method': 'minmax'}
            },
            'highlights': {
                'selection_threshold_std': 0.8,  # Lower threshold for testing
                'min_duration_sec': 15,  # Shorter minimum for testing
                'max_duration_sec': 120,
                'merge_gap_sec': 5,
                'boundary_extension_sentences': 1,
                'top_k': 8,
                'ranking_duration_weight': 0.3
            },
            'output': {
                'version': 'engage-v1.0',
                'include_debug_info': True,
                'validate_schema': False  # Skip schema validation in tests
            }
        }
    
    def test_sample_data_exists(self, sample_data_paths):
        """Test that all sample data files exist."""
        for name, path in sample_data_paths.items():
            assert path.exists(), f"Sample data file missing: {name} at {path}"
            assert path.stat().st_size > 0, f"Sample data file is empty: {name}"
    
    def test_load_sample_transcript(self, sample_data_paths):
        """Test loading the real Whisper transcript."""
        data_loader = DataLoader()
        
        # Load the transcript
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        
        # Validate transcript structure
        assert isinstance(transcript.segments, list)
        assert len(transcript.segments) > 0
        assert transcript.language == "english"
        assert transcript.duration > 3000  # Should be ~61 minutes
        
        # Check first few segments
        first_segment = transcript.segments[0]
        assert first_segment.start == 0.0
        assert "Michio Kaku" in first_segment.text
        
        # Check that segments are properly ordered
        for i in range(len(transcript.segments) - 1):
            current = transcript.segments[i]
            next_seg = transcript.segments[i + 1]
            assert current.end <= next_seg.start + 1.0  # Allow small overlap
    
    def test_load_sample_audio(self, sample_data_paths):
        """Test loading the real audio file."""
        data_loader = DataLoader()
        
        # Load the audio
        audio_data = data_loader.load_audio(sample_data_paths['audio_mp3'])
        
        # Validate audio data
        assert isinstance(audio_data.audio, np.ndarray)
        assert audio_data.sample_rate == 16000
        assert audio_data.duration > 3000  # Should be ~61 minutes
        assert audio_data.channels == 1
        assert len(audio_data.audio) > 0
    
    def test_preprocess_sample_transcript(self, sample_data_paths):
        """Test preprocessing the real transcript data."""
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor()
        
        # Load and preprocess
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        df = preprocessor.process_transcript(transcript, "michio_kaku_interview")
        
        # Validate preprocessing results
        assert len(df) > 0
        assert 'episode_id' in df.columns
        assert 'text' in df.columns
        assert 'start_sec' in df.columns
        assert 'end_sec' in df.columns
        assert 'duration' in df.columns
        
        # Check that we have reasonable number of sentences
        # Should be much more than original segments due to sentence splitting
        assert len(df) > len(transcript.segments)
        
        # Validate data quality
        assert all(df['episode_id'] == "michio_kaku_interview")
        assert all(df['duration'] > 0)
        assert all(df['start_sec'] < df['end_sec'])
        
        # Check for expected content
        all_text = ' '.join(df['text'].tolist())
        assert "artificial intelligence" in all_text.lower()
        assert "physics" in all_text.lower()
        assert "universe" in all_text.lower()
    
    def test_feature_extraction_with_mocked_models(self, sample_data_paths, pipeline_config):
        """Test feature extraction with mocked ML models to avoid loading heavy models."""
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor()
        feature_extractor = FeatureExtractor(pipeline_config['features'])
        
        # Load and preprocess (use subset for faster testing)
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        
        # Take first 50 segments for testing
        transcript.segments = transcript.segments[:50]
        
        df = preprocessor.process_transcript(transcript, "test_episode")
        
        # Mock the heavy ML models
        num_sentences = len(df)
        
        with patch.object(feature_extractor.text_extractor, 'extract_emotion_scores', 
                         return_value=np.random.uniform(0, 1, num_sentences)), \
             patch.object(feature_extractor.text_extractor, 'extract_sentiment_volatility',
                         return_value=np.random.uniform(0, 1, num_sentences)), \
             patch.object(feature_extractor.text_extractor, 'extract_rhetorical_features',
                         return_value=np.random.uniform(0, 1, num_sentences)), \
             patch.object(feature_extractor.text_extractor, 'extract_topic_shifts',
                         return_value=np.random.uniform(0, 1, num_sentences)), \
             patch.object(feature_extractor.text_extractor, 'extract_keyword_salience',
                         return_value=np.random.uniform(0, 1, num_sentences)):
            
            # Extract features (text only for this test)
            df_features = feature_extractor.extract_features(df)
            
            # Validate feature extraction
            expected_text_features = [
                'emotion_score', 'sentiment_volatility', 'rhetorical_score',
                'topic_shift_score', 'keyword_salience'
            ]
            
            for feature in expected_text_features:
                assert feature in df_features.columns
                assert all(0 <= score <= 1 for score in df_features[feature])
            
            # Check audio features are added as zeros (no audio provided)
            audio_features = ['laughter_score', 'applause_score', 'prosody_score', 'clap_similarity']
            for feature in audio_features:
                assert feature in df_features.columns
                assert all(df_features[feature] == 0.0)
    
    def test_engagement_scoring_with_sample_data(self, sample_data_paths, pipeline_config):
        """Test engagement scoring with realistic sample data."""
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor()
        scorer = EngagementScorer(pipeline_config['scoring'])
        
        # Load and preprocess subset
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        transcript.segments = transcript.segments[:30]  # Smaller subset for testing
        
        df = preprocessor.process_transcript(transcript, "test_episode")
        
        # Create realistic feature data
        num_sentences = len(df)
        df['emotion_score'] = np.random.beta(2, 5, num_sentences)  # Skewed toward lower values
        df['sentiment_volatility'] = np.random.exponential(0.3, num_sentences)
        df['rhetorical_score'] = np.random.uniform(0, 0.8, num_sentences)
        df['topic_shift_score'] = np.random.gamma(2, 0.2, num_sentences)
        df['keyword_salience'] = np.random.uniform(0, 1, num_sentences)
        df['laughter_score'] = np.random.exponential(0.1, num_sentences)
        df['applause_score'] = np.random.exponential(0.05, num_sentences)
        df['prosody_score'] = np.random.uniform(0, 1, num_sentences)
        df['clap_similarity'] = np.zeros(num_sentences)
        
        # Calculate engagement scores
        df_scores = scorer.calculate_engagement_scores(df)
        
        # Validate scoring results
        assert 'engagement_score' in df_scores.columns
        assert 'raw_engagement_score' in df_scores.columns
        
        # Check score ranges
        assert all(0 <= score <= 1 for score in df_scores['engagement_score'])
        assert all(0 <= score <= 1 for score in df_scores['raw_engagement_score'])
        
        # Check that smoothing was applied (smoothed should be different from raw)
        assert not np.array_equal(df_scores['engagement_score'], df_scores['raw_engagement_score'])
        
        # Get statistics
        stats = scorer.get_score_statistics(df_scores)
        assert 'mean' in stats
        assert 'std' in stats
        assert stats['count'] == len(df_scores)
    
    def test_highlight_generation_with_sample_data(self, sample_data_paths, pipeline_config):
        """Test highlight generation with realistic engagement scores."""
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor()
        scorer = EngagementScorer(pipeline_config['scoring'])
        highlight_generator = HighlightGenerator(pipeline_config['highlights'])
        
        # Load and preprocess subset
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        transcript.segments = transcript.segments[:100]  # Reasonable subset
        
        df = preprocessor.process_transcript(transcript, "test_episode")
        
        # Create realistic feature data with some high-engagement segments
        num_sentences = len(df)
        
        # Most scores are low, but create some high-engagement peaks
        base_scores = np.random.beta(2, 8, num_sentences)  # Mostly low scores
        
        # Add some high-engagement peaks
        peak_indices = np.random.choice(num_sentences, size=max(1, num_sentences // 20), replace=False)
        base_scores[peak_indices] = np.random.uniform(0.7, 1.0, len(peak_indices))
        
        df['emotion_score'] = base_scores
        df['sentiment_volatility'] = np.random.exponential(0.2, num_sentences)
        df['rhetorical_score'] = np.random.uniform(0, 0.6, num_sentences)
        df['topic_shift_score'] = np.random.gamma(1.5, 0.3, num_sentences)
        df['keyword_salience'] = np.random.uniform(0, 0.8, num_sentences)
        df['laughter_score'] = np.random.exponential(0.1, num_sentences)
        df['applause_score'] = np.random.exponential(0.05, num_sentences)
        df['prosody_score'] = np.random.uniform(0, 0.8, num_sentences)
        df['clap_similarity'] = np.zeros(num_sentences)
        
        # Calculate scores and generate highlights
        df_scores = scorer.calculate_engagement_scores(df)
        highlights = highlight_generator.generate_highlights(df_scores)
        
        # Validate highlights
        assert isinstance(highlights, list)
        
        if highlights:  # May be empty if no segments meet criteria
            # Check highlight properties
            for highlight in highlights:
                assert hasattr(highlight, 'id')
                assert hasattr(highlight, 'start_sec')
                assert hasattr(highlight, 'end_sec')
                assert hasattr(highlight, 'duration')
                assert hasattr(highlight, 'transcript')
                assert hasattr(highlight, 'cues')
                assert hasattr(highlight, 'max_engagement_score')
                
                # Validate highlight data
                assert highlight.start_sec < highlight.end_sec
                assert highlight.duration > 0
                assert highlight.duration >= pipeline_config['highlights']['min_duration_sec']
                assert highlight.duration <= pipeline_config['highlights']['max_duration_sec']
                assert 0 <= highlight.max_engagement_score <= 1
                assert len(highlight.transcript) > 0
                assert isinstance(highlight.cues, list)
            
            # Check that highlights are sorted by ranking score
            ranking_scores = [h.ranking_score for h in highlights]
            assert ranking_scores == sorted(ranking_scores, reverse=True)
    
    def test_json_export_with_sample_highlights(self, pipeline_config):
        """Test JSON export with realistic highlight data."""
        from pipeline.postprocess import HighlightSegment
        
        # Create realistic highlight segments
        mock_sentences = [
            {
                'text': 'So you\'ve talked about different types of civilizations on the Kardashev scale.',
                'engagement_score': 0.85,
                'emotion_score': 0.7,
                'rhetorical_score': 0.9,
                'topic_shift_score': 0.6
            },
            {
                'text': 'What do you think it takes to reach out through communication?',
                'engagement_score': 0.82,
                'emotion_score': 0.6,
                'rhetorical_score': 0.95,
                'topic_shift_score': 0.4
            }
        ]
        
        highlights = [
            HighlightSegment(138.16, 178.72, mock_sentences),
            HighlightSegment(342.48, 398.24, mock_sentences)
        ]
        
        # Initialize exporter
        exporter = JSONExporter(pipeline_config['output'])
        
        # Create processing stats
        processing_stats = {
            'total_duration_sec': 3659.88,
            'segments_analyzed': 1247,
            'processing_time_sec': 287.4
        }
        
        # Generate JSON
        json_data = exporter.create_output_json("michio_kaku_interview", highlights, processing_stats)
        
        # Validate JSON structure
        assert json_data['episode_id'] == "michio_kaku_interview"
        assert json_data['version'] == pipeline_config['output']['version']
        assert 'generated_utc' in json_data
        assert 'processing_metadata' in json_data
        assert 'segments' in json_data
        
        # Validate metadata
        metadata = json_data['processing_metadata']
        assert metadata['total_duration_sec'] == 3659.88
        assert metadata['segments_analyzed'] == 1247
        assert metadata['llm_reranked'] == False
        assert 'features_used' in metadata
        
        # Validate segments
        assert len(json_data['segments']) == len(highlights)
        
        for i, segment_data in enumerate(json_data['segments']):
            highlight = highlights[i]
            
            # Check required fields
            required_fields = ['id', 'start_sec', 'end_sec', 'duration', 'cues', 'confidence', 'transcript']
            assert all(field in segment_data for field in required_fields)
            
            # Check values
            assert segment_data['start_sec'] == highlight.start_sec
            assert segment_data['end_sec'] == highlight.end_sec
            assert segment_data['duration'] == highlight.duration
            assert segment_data['confidence'] == highlight.max_engagement_score
            assert len(segment_data['transcript']) > 0
    
    def test_end_to_end_pipeline_with_sample_data(self, sample_data_paths, pipeline_config):
        """Test the complete pipeline end-to-end with real sample data."""
        # Initialize all components
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor()
        feature_extractor = FeatureExtractor(pipeline_config['features'])
        scorer = EngagementScorer(pipeline_config['scoring'])
        highlight_generator = HighlightGenerator(pipeline_config['highlights'])
        exporter = JSONExporter(pipeline_config['output'])
        
        # Load sample data (use subset for reasonable test time)
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        
        # Use first 10 minutes of content for testing
        subset_segments = []
        for segment in transcript.segments:
            if segment.start < 600:  # 10 minutes
                subset_segments.append(segment)
            else:
                break
        
        transcript.segments = subset_segments
        
        # Mock the heavy ML models for testing
        with patch.object(feature_extractor.text_extractor, 'extract_emotion_scores') as mock_emotion, \
             patch.object(feature_extractor.text_extractor, 'extract_sentiment_volatility') as mock_sentiment, \
             patch.object(feature_extractor.text_extractor, 'extract_rhetorical_features') as mock_rhetorical, \
             patch.object(feature_extractor.text_extractor, 'extract_topic_shifts') as mock_topic, \
             patch.object(feature_extractor.text_extractor, 'extract_keyword_salience') as mock_keyword:
            
            # Step 1: Preprocess
            df = preprocessor.process_transcript(transcript, "michio_kaku_sample")
            
            # Configure mocks to return realistic data
            num_sentences = len(df)
            mock_emotion.return_value = np.random.beta(2, 5, num_sentences)
            mock_sentiment.return_value = np.random.exponential(0.3, num_sentences)
            mock_rhetorical.return_value = np.random.uniform(0, 0.8, num_sentences)
            mock_topic.return_value = np.random.gamma(2, 0.2, num_sentences)
            mock_keyword.return_value = np.random.uniform(0, 1, num_sentences)
            
            # Step 2: Extract features
            df_features = feature_extractor.extract_features(df)
            
            # Step 3: Calculate engagement scores
            df_scores = scorer.calculate_engagement_scores(df_features)
            
            # Step 4: Generate highlights
            highlights = highlight_generator.generate_highlights(df_scores)
            
            # Step 5: Export to JSON
            processing_stats = {
                'total_duration_sec': 600.0,  # 10 minutes
                'segments_analyzed': len(df),
                'processing_time_sec': 45.2
            }
            
            json_data = exporter.create_output_json("michio_kaku_sample", highlights, processing_stats)
            
            # Validate end-to-end results
            assert json_data['episode_id'] == "michio_kaku_sample"
            assert len(df_scores) == num_sentences
            assert 'engagement_score' in df_scores.columns
            assert isinstance(highlights, list)
            assert len(json_data['segments']) == len(highlights)
            
            # Test export to file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                temp_path = f.name
            
            try:
                success = exporter.export_highlights("michio_kaku_sample", highlights, processing_stats, temp_path)
                assert success
                assert Path(temp_path).exists()
                
                # Validate exported file
                with open(temp_path, 'r') as f:
                    exported_data = json.load(f)
                
                assert exported_data['episode_id'] == "michio_kaku_sample"
                assert len(exported_data['segments']) == len(highlights)
                
            finally:
                # Clean up
                Path(temp_path).unlink(missing_ok=True)
    
    def test_sample_data_content_validation(self, sample_data_paths):
        """Test that sample data contains expected content for engagement detection."""
        data_loader = DataLoader()
        transcript = data_loader.load_transcript(sample_data_paths['transcript_json'])
        
        # Combine all text
        all_text = ' '.join(segment.text for segment in transcript.segments).lower()
        
        # Check for content that should generate engagement
        engagement_indicators = [
            'question',  # Questions should trigger rhetorical features
            '?',         # Question marks
            '!',         # Exclamations
            'you',       # Second person pronouns
            'amazing',   # Emotional words
            'incredible',
            'fascinating',
            'beautiful',
            'exciting'
        ]
        
        found_indicators = []
        for indicator in engagement_indicators:
            if indicator in all_text:
                found_indicators.append(indicator)
        
        # Should find most engagement indicators in this rich content
        assert len(found_indicators) >= len(engagement_indicators) * 0.7, \
            f"Expected to find most engagement indicators, found: {found_indicators}"

        # Check for topic diversity (should trigger topic shift detection)
        topics = [
            'physics', 'universe', 'alien', 'civilization', 'technology',
            'artificial intelligence', 'robot', 'future', 'mars', 'space'
        ]

        found_topics = [topic for topic in topics if topic in all_text]
        assert len(found_topics) >= 5, f"Expected diverse topics, found: {found_topics}"


# Simple test runner for when pytest is not available
def run_tests_standalone():
    """Run tests without pytest for basic validation."""
    print("=== Running Sample Data Tests (Standalone Mode) ===\n")

    test_instance = TestSampleDataPipeline()

    # Get fixtures
    sample_data_paths = test_instance.sample_data_paths()
    pipeline_config = test_instance.pipeline_config()

    # List of test methods to run
    test_methods = [
        ("Sample Data Existence", test_instance.test_sample_data_exists),
        ("Load Sample Transcript", test_instance.test_load_sample_transcript),
        ("Load Sample Audio", test_instance.test_load_sample_audio),
        ("Preprocess Sample Transcript", test_instance.test_preprocess_sample_transcript),
        ("Sample Data Content Validation", test_instance.test_sample_data_content_validation)
    ]

    passed = 0
    total = len(test_methods)

    for test_name, test_method in test_methods:
        try:
            print(f"Running: {test_name}")

            # Call test method with fixtures
            if 'sample_data_paths' in test_method.__code__.co_varnames:
                if 'pipeline_config' in test_method.__code__.co_varnames:
                    result = test_method(sample_data_paths, pipeline_config)
                else:
                    result = test_method(sample_data_paths)
            else:
                result = test_method()

            if result is not False:  # None or True means success
                print(f"✓ {test_name}: PASSED\n")
                passed += 1
            else:
                print(f"✗ {test_name}: FAILED\n")

        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}\n")

    print(f"=== Test Summary ===")
    print(f"Passed: {passed}/{total} tests")

    if passed == total:
        print("🎉 All sample data tests passed!")
        return True
    else:
        print("❌ Some tests failed.")
        return False


if __name__ == '__main__':
    """Run tests when script is executed directly."""
    if PYTEST_AVAILABLE:
        print("pytest is available. Run with: python -m pytest tests/test_sample_data.py -v")
    else:
        print("pytest not available. Running in standalone mode...")
        success = run_tests_standalone()
        exit(0 if success else 1)
