"""
Integration Tests for High-Level APIs

Tests the integration APIs to ensure they work correctly
for external projects.
"""

import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch
import numpy as np
import pandas as pd

import sys
sys.path.append(str(Path(__file__).parent.parent))

from api import EngagementDetector, QuickProcessor, quick_highlights, detect_engagement
from pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData


class TestEngagementDetector:
    """Test the high-level EngagementDetector API."""
    
    def test_initialization_default_config(self):
        """Test detector initialization with default config."""
        detector = EngagementDetector()
        
        assert detector.config is not None
        assert 'features' in detector.config
        assert 'scoring' in detector.config
        assert 'highlights' in detector.config
        
        # Check components are initialized
        assert detector.data_loader is not None
        assert detector.preprocessor is not None
        assert detector.feature_extractor is not None
        assert detector.scorer is not None
        assert detector.highlight_generator is not None
        assert detector.json_exporter is not None
    
    def test_initialization_custom_config(self):
        """Test detector initialization with custom config."""
        custom_config = {
            'features': {'text': {'emotion_threshold': 0.2}},
            'scoring': {'weights': {'emotion': 0.5}},
            'highlights': {'top_k': 3}
        }
        
        detector = EngagementDetector(custom_config)
        
        assert detector.config == custom_config
        assert detector.config['highlights']['top_k'] == 3
    
    @patch('engagement_app.api.DataLoader')
    @patch('engagement_app.api.FeatureExtractor')
    @patch('engagement_app.api.EngagementScorer')
    @patch('engagement_app.api.HighlightGenerator')
    def test_process_files(self, mock_highlight_gen, mock_scorer, mock_feature_ext, mock_data_loader):
        """Test processing files through the API."""
        # Setup mocks
        mock_transcript = Mock()
        mock_audio = Mock()
        mock_df = pd.DataFrame({'text': ['test'], 'engagement_score': [0.8]})
        mock_highlights = [Mock()]
        
        mock_data_loader.return_value.load_transcript.return_value = mock_transcript
        mock_data_loader.return_value.load_audio.return_value = mock_audio
        mock_data_loader.return_value.validate_alignment.return_value = True
        
        # Mock pipeline components
        detector = EngagementDetector()
        detector.preprocessor.process_transcript = Mock(return_value=mock_df)
        detector.feature_extractor.extract_features = Mock(return_value=mock_df)
        detector.scorer.calculate_engagement_scores = Mock(return_value=mock_df)
        detector.highlight_generator.generate_highlights = Mock(return_value=mock_highlights)
        
        # Test
        result = detector.process_files("transcript.json", "audio.wav", "test_episode")
        
        assert result == mock_highlights
        detector.preprocessor.process_transcript.assert_called_once_with(mock_transcript, "test_episode")
        detector.feature_extractor.extract_features.assert_called_once_with(mock_df, mock_audio)
    
    def test_export_highlights(self):
        """Test exporting highlights to JSON."""
        detector = EngagementDetector()
        
        # Mock highlights
        mock_highlights = [Mock()]
        mock_highlights[0].start_sec = 10.0
        mock_highlights[0].end_sec = 20.0
        
        # Mock exporter
        detector.json_exporter.export_highlights = Mock(return_value=True)
        
        result = detector.export_highlights(
            highlights=mock_highlights,
            episode_id="test",
            output_path="output.json"
        )
        
        assert result is True
        detector.json_exporter.export_highlights.assert_called_once()


class TestQuickProcessor:
    """Test the QuickProcessor simplified API."""
    
    @patch('engagement_app.api.EngagementDetector')
    def test_extract_highlights(self, mock_detector_class):
        """Test quick highlight extraction."""
        # Setup mock
        mock_detector = Mock()
        mock_highlight = Mock()
        mock_highlight.start_sec = 10.0
        mock_highlight.end_sec = 20.0
        mock_highlight.duration = 10.0
        mock_highlight.transcript = "Test transcript"
        mock_highlight.max_engagement_score = 0.8
        mock_highlight.cues = ["emotion", "laughter"]
        
        mock_detector.process_files.return_value = [mock_highlight]
        mock_detector_class.return_value = mock_detector
        
        # Test
        result = QuickProcessor.extract_highlights(
            transcript_path="transcript.json",
            audio_path="audio.wav",
            top_k=5
        )
        
        assert len(result) == 1
        assert result[0]['start_sec'] == 10.0
        assert result[0]['end_sec'] == 20.0
        assert result[0]['duration'] == 10.0
        assert result[0]['transcript'] == "Test transcript"
        assert result[0]['engagement_score'] == 0.8
        assert result[0]['cues'] == ["emotion", "laughter"]
    
    @patch('engagement_app.api.EngagementDetector')
    def test_process_to_json(self, mock_detector_class):
        """Test processing directly to JSON file."""
        # Setup mock
        mock_detector = Mock()
        mock_detector.process_files.return_value = [Mock(), Mock(), Mock()]  # 3 highlights
        mock_detector.export_highlights.return_value = True
        mock_detector_class.return_value = mock_detector
        
        # Test
        result = QuickProcessor.process_to_json(
            transcript_path="transcript.json",
            output_path="output.json",
            audio_path="audio.wav",
            top_k=2
        )
        
        assert result is True
        mock_detector.process_files.assert_called_once()
        mock_detector.export_highlights.assert_called_once()
        
        # Check that highlights were limited to top_k
        call_args = mock_detector.export_highlights.call_args
        highlights_arg = call_args[1]['highlights']  # keyword argument
        assert len(highlights_arg) == 2  # Limited to top_k


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    @patch('engagement_app.api.EngagementDetector')
    def test_detect_engagement(self, mock_detector_class):
        """Test detect_engagement convenience function."""
        mock_detector = Mock()
        mock_highlights = [Mock()]
        mock_detector.process_files.return_value = mock_highlights
        mock_detector_class.return_value = mock_detector
        
        result = detect_engagement("transcript.json", "audio.wav")
        
        assert result == mock_highlights
        mock_detector_class.assert_called_once_with(None)
        mock_detector.process_files.assert_called_once_with("transcript.json", "audio.wav")
    
    @patch('engagement_app.api.QuickProcessor')
    def test_quick_highlights(self, mock_processor_class):
        """Test quick_highlights convenience function."""
        mock_highlights = [{'start_sec': 10, 'end_sec': 20}]
        mock_processor_class.extract_highlights.return_value = mock_highlights
        
        result = quick_highlights("transcript.json", "audio.wav", top_k=3)
        
        assert result == mock_highlights
        mock_processor_class.extract_highlights.assert_called_once_with(
            "transcript.json", "audio.wav", 3
        )


class TestIntegrationScenarios:
    """Test realistic integration scenarios."""
    
    def test_minimal_integration(self):
        """Test minimal integration with mocked data."""
        # Create minimal transcript data
        transcript_data = {
            "segments": [
                {"start": 0.0, "end": 5.0, "text": "Hello world"},
                {"start": 5.0, "end": 10.0, "text": "This is amazing!"},
                {"start": 10.0, "end": 15.0, "text": "What do you think?"}
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(transcript_data, f)
            transcript_path = f.name
        
        try:
            # Mock the heavy processing parts
            with patch('engagement_app.pipeline.features.TextFeatureExtractor') as mock_text_ext, \
                 patch('engagement_app.pipeline.features.AudioFeatureExtractor') as mock_audio_ext:
                
                # Setup mocks to return simple data
                mock_text_ext.return_value.extract_all_text_features.return_value = pd.DataFrame({
                    'text': ['Hello world', 'This is amazing!', 'What do you think?'],
                    'emotion_score': [0.1, 0.9, 0.3],
                    'sentiment_volatility': [0.0, 0.5, 0.2],
                    'rhetorical_score': [0.0, 0.1, 0.8],
                    'topic_shift_score': [0.0, 0.2, 0.1],
                    'keyword_salience': [0.1, 0.3, 0.2],
                    'start_sec': [0.0, 5.0, 10.0],
                    'end_sec': [5.0, 10.0, 15.0]
                })
                
                mock_audio_ext.return_value.extract_all_audio_features.return_value = pd.DataFrame({
                    'laughter_score': [0.0, 0.7, 0.0],
                    'applause_score': [0.0, 0.0, 0.0],
                    'prosody_score': [0.2, 0.8, 0.3],
                    'clap_similarity': [0.0, 0.0, 0.0]
                })
                
                # Test the integration
                highlights = quick_highlights(transcript_path, top_k=2)
                
                assert isinstance(highlights, list)
                assert len(highlights) <= 2
                
                if highlights:
                    highlight = highlights[0]
                    assert 'start_sec' in highlight
                    assert 'end_sec' in highlight
                    assert 'transcript' in highlight
                    assert 'engagement_score' in highlight
                    assert 'cues' in highlight
        
        finally:
            Path(transcript_path).unlink()  # Clean up
    
    def test_custom_config_integration(self):
        """Test integration with custom configuration."""
        custom_config = {
            'features': {
                'text': {'emotion_threshold': 0.1},
                'audio': {'sample_rate': 16000}
            },
            'scoring': {
                'weights': {
                    'emotion': 0.5,
                    'laughter': 0.3,
                    'sentiment_delta': 0.1,
                    'rhetorical': 0.05,
                    'topic_shift': 0.05
                }
            },
            'highlights': {
                'min_duration_sec': 10,
                'max_duration_sec': 60,
                'top_k': 3
            }
        }
        
        detector = EngagementDetector(custom_config)
        
        # Verify config was applied
        assert detector.config == custom_config
        assert detector.config['scoring']['weights']['emotion'] == 0.5
        assert detector.config['highlights']['top_k'] == 3
    
    def test_error_handling(self):
        """Test error handling in integration scenarios."""
        # Test with non-existent file
        with pytest.raises(Exception):
            quick_highlights("nonexistent.json")
        
        # Test with invalid config
        invalid_config = {
            'scoring': {
                'weights': {
                    'invalid_feature': 1.0  # This should cause issues
                }
            }
        }
        
        # Should not crash during initialization
        detector = EngagementDetector(invalid_config)
        assert detector is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
