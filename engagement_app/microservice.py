"""
Microservice API for Audience Engagement Detection

Provides REST API endpoints for integrating engagement detection
into microservice architectures.
"""

import os
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import json

from flask import Flask, request, jsonify, send_file
from werkzeug.utils import secure_filename
import structlog

from .api import EngagementDetector, QuickProcessor

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# Global detector instance for reuse
detector = None


def get_detector(config: Optional[Dict[str, Any]] = None) -> EngagementDetector:
    """Get or create detector instance."""
    global detector
    if detector is None or config is not None:
        detector = EngagementDetector(config)
    return detector


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        # Quick validation that models can be loaded
        test_detector = get_detector()
        return jsonify({
            'status': 'healthy',
            'service': 'vido-tide-engagement-detection',
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500


@app.route('/analyze', methods=['POST'])
def analyze_engagement():
    """
    Analyze engagement from uploaded files.
    
    Expects:
    - transcript: JSON file (required)
    - audio: Audio file (optional)
    - config: JSON configuration (optional)
    - top_k: Number of highlights to return (optional, default: 5)
    """
    try:
        # Validate request
        if 'transcript' not in request.files:
            return jsonify({'error': 'Transcript file required'}), 400
        
        transcript_file = request.files['transcript']
        audio_file = request.files.get('audio')
        
        # Get parameters
        top_k = int(request.form.get('top_k', 5))
        episode_id = request.form.get('episode_id', 'episode')
        
        # Get custom configuration if provided
        config = None
        if 'config' in request.form:
            try:
                config = json.loads(request.form['config'])
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid JSON configuration'}), 400
        
        # Process files in temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save transcript
            transcript_path = os.path.join(temp_dir, secure_filename(transcript_file.filename))
            transcript_file.save(transcript_path)
            
            # Save audio if provided
            audio_path = None
            if audio_file:
                audio_path = os.path.join(temp_dir, secure_filename(audio_file.filename))
                audio_file.save(audio_path)
            
            # Process with appropriate method
            if config:
                # Use full detector with custom config
                detector_instance = get_detector(config)
                highlights = detector_instance.process_files(
                    transcript_path=transcript_path,
                    audio_path=audio_path,
                    episode_id=episode_id
                )
                
                # Convert to simplified format and limit
                simplified_highlights = [
                    {
                        'id': h.id,
                        'start_sec': h.start_sec,
                        'end_sec': h.end_sec,
                        'duration': h.duration,
                        'transcript': h.transcript,
                        'engagement_score': h.max_engagement_score,
                        'mean_engagement_score': h.mean_engagement_score,
                        'cues': h.cues,
                        'feature_scores': h._get_feature_scores()
                    }
                    for h in highlights[:top_k]
                ]
            else:
                # Use quick processor
                simplified_highlights = QuickProcessor.extract_highlights(
                    transcript_path=transcript_path,
                    audio_path=audio_path,
                    top_k=top_k
                )
            
            logger.info("Analysis completed", 
                       episode_id=episode_id, 
                       highlights_count=len(simplified_highlights),
                       has_audio=audio_file is not None)
            
            return jsonify({
                'status': 'success',
                'episode_id': episode_id,
                'highlights': simplified_highlights,
                'count': len(simplified_highlights),
                'has_audio_features': audio_file is not None
            })
    
    except Exception as e:
        logger.error("Analysis failed", error=str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500


@app.route('/analyze-json', methods=['POST'])
def analyze_engagement_json():
    """
    Analyze engagement from JSON payload.
    
    Expects JSON with:
    - transcript_path: Path to transcript file
    - audio_path: Path to audio file (optional)
    - config: Configuration object (optional)
    - top_k: Number of highlights (optional)
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'JSON payload required'}), 400
        
        transcript_path = data.get('transcript_path')
        if not transcript_path:
            return jsonify({'error': 'transcript_path required'}), 400
        
        audio_path = data.get('audio_path')
        config = data.get('config')
        top_k = data.get('top_k', 5)
        episode_id = data.get('episode_id', Path(transcript_path).stem)
        
        # Validate file exists
        if not os.path.exists(transcript_path):
            return jsonify({'error': f'Transcript file not found: {transcript_path}'}), 400
        
        if audio_path and not os.path.exists(audio_path):
            return jsonify({'error': f'Audio file not found: {audio_path}'}), 400
        
        # Process
        if config:
            detector_instance = get_detector(config)
            highlights = detector_instance.process_files(
                transcript_path=transcript_path,
                audio_path=audio_path,
                episode_id=episode_id
            )
            
            simplified_highlights = [
                {
                    'id': h.id,
                    'start_sec': h.start_sec,
                    'end_sec': h.end_sec,
                    'duration': h.duration,
                    'transcript': h.transcript,
                    'engagement_score': h.max_engagement_score,
                    'mean_engagement_score': h.mean_engagement_score,
                    'cues': h.cues,
                    'feature_scores': h._get_feature_scores()
                }
                for h in highlights[:top_k]
            ]
        else:
            simplified_highlights = QuickProcessor.extract_highlights(
                transcript_path=transcript_path,
                audio_path=audio_path,
                top_k=top_k
            )
        
        logger.info("JSON analysis completed", 
                   episode_id=episode_id, 
                   highlights_count=len(simplified_highlights))
        
        return jsonify({
            'status': 'success',
            'episode_id': episode_id,
            'highlights': simplified_highlights,
            'count': len(simplified_highlights)
        })
    
    except Exception as e:
        logger.error("JSON analysis failed", error=str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500


@app.route('/export', methods=['POST'])
def export_highlights():
    """
    Export highlights to JSON file.
    
    Expects same input as /analyze but returns downloadable JSON file.
    """
    try:
        # Validate request
        if 'transcript' not in request.files:
            return jsonify({'error': 'Transcript file required'}), 400
        
        transcript_file = request.files['transcript']
        audio_file = request.files.get('audio')
        
        # Get parameters
        top_k = int(request.form.get('top_k', 10))
        episode_id = request.form.get('episode_id', 'episode')
        
        # Get custom configuration if provided
        config = None
        if 'config' in request.form:
            try:
                config = json.loads(request.form['config'])
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid JSON configuration'}), 400
        
        # Process files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save input files
            transcript_path = os.path.join(temp_dir, secure_filename(transcript_file.filename))
            transcript_file.save(transcript_path)
            
            audio_path = None
            if audio_file:
                audio_path = os.path.join(temp_dir, secure_filename(audio_file.filename))
                audio_file.save(audio_path)
            
            # Process and export
            detector_instance = get_detector(config)
            highlights = detector_instance.process_files(
                transcript_path=transcript_path,
                audio_path=audio_path,
                episode_id=episode_id
            )
            
            # Limit highlights
            if len(highlights) > top_k:
                highlights = highlights[:top_k]
            
            # Export to JSON file
            output_path = os.path.join(temp_dir, f"{episode_id}_highlights.json")
            success = detector_instance.export_highlights(
                highlights=highlights,
                episode_id=episode_id,
                output_path=output_path
            )
            
            if not success:
                return jsonify({'error': 'Failed to export highlights'}), 500
            
            logger.info("Export completed", 
                       episode_id=episode_id, 
                       highlights_count=len(highlights))
            
            return send_file(
                output_path,
                as_attachment=True,
                download_name=f"{episode_id}_highlights.json",
                mimetype='application/json'
            )
    
    except Exception as e:
        logger.error("Export failed", error=str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500


@app.route('/config/default', methods=['GET'])
def get_default_config():
    """Get the default configuration."""
    try:
        detector_instance = get_detector()
        return jsonify({
            'status': 'success',
            'config': detector_instance.config
        })
    except Exception as e:
        logger.error("Failed to get default config", error=str(e))
        return jsonify({'error': str(e)}), 500


@app.route('/config/validate', methods=['POST'])
def validate_config():
    """Validate a configuration object."""
    try:
        config = request.get_json()
        if not config:
            return jsonify({'error': 'JSON configuration required'}), 400
        
        # Try to create detector with config
        test_detector = EngagementDetector(config)
        
        return jsonify({
            'status': 'valid',
            'message': 'Configuration is valid'
        })
    
    except Exception as e:
        logger.error("Config validation failed", error=str(e))
        return jsonify({
            'status': 'invalid',
            'error': str(e)
        }), 400


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    return jsonify({'error': 'File too large. Maximum size is 500MB.'}), 413


@app.errorhandler(404)
def not_found(e):
    """Handle not found error."""
    return jsonify({'error': 'Endpoint not found'}), 404


@app.errorhandler(500)
def internal_error(e):
    """Handle internal server error."""
    logger.error("Internal server error", error=str(e))
    return jsonify({'error': 'Internal server error'}), 500


def create_app(config: Optional[Dict[str, Any]] = None) -> Flask:
    """Create Flask app with optional configuration."""
    if config:
        app.config.update(config)
    return app


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run development server
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info("Starting Vido Tide microservice", port=port, debug=debug)
    app.run(host='0.0.0.0', port=port, debug=debug)
