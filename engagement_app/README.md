# Audience Engagement Detection Pipeline

A production-ready system for detecting audience engagement from long-form audio content using unsupervised methods. Processes OpenAI Whisper transcripts and raw audio files to output validated JSON containing highlight segments with timestamps, engagement cues, and confidence scores.

## Features

- **Multi-modal Analysis**: Combines text and audio features for comprehensive engagement detection
- **Unsupervised Learning**: No training data required, works out-of-the-box
- **Production Ready**: Comprehensive error handling, logging, and validation
- **Scalable Architecture**: Modular design with configurable components
- **LLM Integration**: Optional re-ranking using OpenAI GPT-4o or Google Gemini
- **Schema Validation**: Validated JSON output with comprehensive metadata

## Quick Start

### Installation

1. **Clone and setup environment**:
```bash
cd engagement_app
pip install -r requirements.txt
```

2. **Install required models**:
```bash
python -m spacy download en_core_web_sm
python -c "import nltk; nltk.download('vader_lexicon')"
```

3. **Set up API keys** (optional, for LLM re-ranking):
```bash
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
```

### Basic Usage

**Text-only analysis**:
```bash
python scripts/run_pipeline.py \
  --episode_id "podcast_ep_123" \
  --transcript_path "data/transcripts/episode.json" \
  --output_dir "outputs/"
```

**Full pipeline with audio**:
```bash
python scripts/run_pipeline.py \
  --episode_id "podcast_ep_123" \
  --audio_path "data/raw_audio/episode.wav" \
  --transcript_path "data/transcripts/episode.json" \
  --output_dir "outputs/" \
  --use_llm --top_k 15
```

## Architecture

### Pipeline Components

1. **Data Loading** (`load_data.py`)
   - Whisper transcript parsing and validation
   - Audio file loading with format support
   - Duration alignment verification

2. **Preprocessing** (`preprocess.py`)
   - Sentence-level segmentation using spaCy
   - Proportional timestamp interpolation
   - Text cleaning and normalization

3. **Feature Extraction** (`features.py`)
   - **Text Features**: Emotion scores, sentiment volatility, rhetorical patterns, topic shifts, keyword salience
   - **Audio Features**: Laughter/applause detection, prosodic emphasis, RMS energy analysis

4. **Engagement Scoring** (`scoring.py`)
   - Weighted feature fusion with configurable weights
   - Min-max normalization and temporal smoothing
   - Statistical analysis and debugging metrics

5. **Highlight Generation** (`postprocess.py`)
   - Candidate selection using statistical thresholds
   - Boundary expansion and segment merging
   - Duration filtering and ranking algorithms

6. **LLM Re-ranking** (`llm_rerank.py`) *Optional*
   - OpenAI GPT-4o and Google Gemini integration
   - Context-aware segment evaluation
   - Confidence-based filtering

7. **JSON Export** (`export_json.py`)
   - Schema-validated output generation
   - Comprehensive metadata inclusion
   - Pretty-printed JSON with debugging info

### Feature Details

#### Text Features
- **Emotion Scores**: Uses `joeddav/distilbert-go-emotions` to detect joy, anger, surprise, excitement
- **Sentiment Volatility**: VADER compound score differences between consecutive sentences
- **Rhetorical Engagement**: Questions, exclamations, second-person pronouns, intensifiers
- **Topic Shifts**: BERTopic with Jensen-Shannon divergence on sliding windows
- **Keyword Salience**: TF-IDF + KeyBERT combination with document frequency normalization

#### Audio Features
- **Laughter/Applause**: PANNs AudioSet model with 0.5 threshold
- **Prosodic Emphasis**: RMS energy analysis with statistical outlier detection
- **Future**: CLAP similarity between audio and text embeddings

#### Scoring Algorithm
```
engagement_score = 0.30×emotion + 0.15×sentiment_delta + 0.10×rhetorical + 
                   0.20×topic_shift + 0.05×keyword_salience + 0.07×laughter + 
                   0.05×applause + 0.05×prosody + 0.03×clap_similarity
```

## Configuration

The pipeline is highly configurable via `config/pipeline_config.yaml`:

```yaml
# Feature weights
scoring:
  weights:
    emotion: 0.30
    sentiment_delta: 0.15
    rhetorical: 0.10
    # ... more weights

# Highlight generation
highlights:
  selection_threshold_std: 1.0
  min_duration_sec: 30
  max_duration_sec: 180
  top_k: 10

# LLM settings
llm:
  enabled: false
  provider: "openai"
  confidence_threshold: 0.6
```

## Input/Output Formats

### Input: Whisper Transcript
```json
{
  "segments": [
    {
      "start": 0.0,
      "end": 5.2,
      "text": "Welcome to our podcast about AI and technology."
    }
  ],
  "language": "en",
  "duration": 3600.0
}
```

### Output: Engagement Highlights
```json
{
  "episode_id": "podcast_ep_123",
  "generated_utc": "2024-01-15T10:30:00Z",
  "version": "engage-v1.0",
  "processing_metadata": {
    "total_duration_sec": 3600.0,
    "segments_analyzed": 450,
    "llm_reranked": true,
    "processing_time_sec": 285.6
  },
  "segments": [
    {
      "id": "uuid-string",
      "start_sec": 120.5,
      "end_sec": 165.2,
      "duration": 44.7,
      "cues": ["high_emotion", "laughter", "rhetorical_engagement"],
      "confidence": 0.87,
      "transcript": "Segment transcript text...",
      "feature_scores": {
        "emotion": 0.85,
        "sentiment_volatility": 0.45,
        "rhetorical": 0.72
      }
    }
  ]
}
```

## Performance

- **Target**: Process 1 hour of audio in <7.5 minutes on NVIDIA T4
- **Memory**: Optimized for 8GB RAM with chunking and caching
- **Batch Processing**: Configurable batch sizes for transformer models
- **GPU Acceleration**: Automatic CUDA detection with CPU fallback

## Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov pytest-mock

# Run all tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=pipeline --cov-report=html

# Run specific test modules
pytest tests/test_preprocess.py -v
pytest tests/test_features.py -v
```

## API Reference

### Core Classes

#### `DataLoader`
```python
loader = DataLoader(target_sample_rate=16000)
transcript = loader.load_transcript("transcript.json")
audio = loader.load_audio("audio.wav")
```

#### `FeatureExtractor`
```python
extractor = FeatureExtractor(config['features'])
df_with_features = extractor.extract_features(df, audio_data)
```

#### `EngagementScorer`
```python
scorer = EngagementScorer(config['scoring'])
df_with_scores = scorer.calculate_engagement_scores(df)
```

#### `HighlightGenerator`
```python
generator = HighlightGenerator(config['highlights'])
highlights = generator.generate_highlights(df)
```

## Troubleshooting

### Common Issues

1. **spaCy model not found**:
   ```bash
   python -m spacy download en_core_web_sm
   ```

2. **NLTK data missing**:
   ```bash
   python -c "import nltk; nltk.download('vader_lexicon')"
   ```

3. **GPU memory issues**:
   - Reduce `batch_size` in config
   - Set `use_gpu: false` in config

4. **Audio loading errors**:
   - Install additional codecs: `pip install ffmpeg-python`
   - Check supported formats: `.wav`, `.mp3`, `.flac`, `.m4a`, `.ogg`

### Logging

Enable debug logging for detailed information:
```bash
python scripts/run_pipeline.py --log_level DEBUG --log_file pipeline.log
```

## Contributing

1. Follow PEP 8 style guidelines
2. Add type hints to all functions
3. Include comprehensive docstrings
4. Write tests for new features
5. Update configuration schema as needed

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Citation

If you use this pipeline in your research, please cite:

```bibtex
@software{engagement_detection_pipeline,
  title={Audience Engagement Detection Pipeline},
  author={MLOps Engineering Team},
  year={2024},
  version={1.0.0},
  url={https://github.com/your-org/engagement-pipeline}
}
```
