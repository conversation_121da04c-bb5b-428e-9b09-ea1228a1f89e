"""
LLM Re-ranking Module

Provides optional LLM-based re-ranking of highlight segments using
OpenAI GPT-4o or Google Gemini models.
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional, Tuple

import requests
from openai import OpenAI
import google.generativeai as genai

from .postprocess import HighlightSegment

logger = logging.getLogger(__name__)


class LLMReranker:
    """
    Re-ranks highlight segments using Large Language Models.
    
    Supports OpenAI GPT-4o and Google Gemini-1.5-pro for evaluating
    audience engagement in transcript segments.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize LLM reranker with configuration.
        
        Args:
            config: Configuration dictionary with LLM settings
        """
        self.config = config
        self.enabled = config.get('enabled', False)
        self.provider = config.get('provider', 'openai')
        self.model = config.get('model', 'gpt-4o')
        self.max_segment_duration = config.get('max_segment_duration', 45)
        self.confidence_threshold = config.get('confidence_threshold', 0.6)
        self.rate_limit_delay = config.get('rate_limit_delay', 1.0)
        self.max_retries = config.get('max_retries', 3)
        self.context_sentences = config.get('context_sentences', 2)
        
        # Initialize clients
        self._openai_client = None
        self._genai_configured = False
        
        if self.enabled:
            self._initialize_clients()
        
        logger.info(f"Initialized LLM reranker: enabled={self.enabled}, provider={self.provider}")
    
    def _initialize_clients(self):
        """Initialize LLM API clients."""
        try:
            if self.provider == 'openai':
                import os
                api_key = os.getenv('OPENAI_API_KEY')
                if not api_key:
                    logger.error("OPENAI_API_KEY environment variable not set")
                    self.enabled = False
                    return
                self._openai_client = OpenAI(api_key=api_key)
                logger.info("Initialized OpenAI client")
                
            elif self.provider == 'google':
                import os
                api_key = os.getenv('GOOGLE_API_KEY')
                if not api_key:
                    logger.error("GOOGLE_API_KEY environment variable not set")
                    self.enabled = False
                    return
                genai.configure(api_key=api_key)
                self._genai_configured = True
                logger.info("Configured Google Generative AI")
                
        except Exception as e:
            logger.error(f"Failed to initialize LLM client: {e}")
            self.enabled = False
    
    def _create_prompt(self, segment: HighlightSegment, context: str = "") -> str:
        """
        Create evaluation prompt for LLM.
        
        Args:
            segment: Highlight segment to evaluate
            context: Optional context from surrounding segments
            
        Returns:
            Formatted prompt string
        """
        prompt = f"""Evaluate the audience engagement level for this audio transcript segment from a podcast or presentation.

Consider the following factors when assessing engagement:
1. Emotional tone and intensity
2. Humor, wit, or entertaining content
3. Surprising or unexpected information
4. Audience reactions (laughter, applause)
5. Rhetorical devices (questions, emphasis)
6. Topic shifts or revelations
7. Personal stories or anecdotes

Transcript segment ({segment.duration:.1f} seconds):
"{segment.transcript}"

{f"Context: {context}" if context else ""}

Respond in JSON format with the following structure:
{{
    "engaged": boolean,
    "confidence": float (0.0 to 1.0),
    "primary_cues": [list of engagement cues detected],
    "reasoning": "brief explanation of the assessment"
}}

Focus on content that would genuinely capture and hold an audience's attention."""
        
        return prompt
    
    def _call_openai(self, prompt: str) -> Optional[Dict[str, Any]]:
        """
        Call OpenAI API for segment evaluation.
        
        Args:
            prompt: Evaluation prompt
            
        Returns:
            Parsed response dictionary or None if failed
        """
        if not self._openai_client:
            return None
        
        try:
            response = self._openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in audience engagement analysis for audio content."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse OpenAI response as JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return None
    
    def _call_google(self, prompt: str) -> Optional[Dict[str, Any]]:
        """
        Call Google Generative AI for segment evaluation.
        
        Args:
            prompt: Evaluation prompt
            
        Returns:
            Parsed response dictionary or None if failed
        """
        if not self._genai_configured:
            return None
        
        try:
            model = genai.GenerativeModel(self.model)
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,
                    max_output_tokens=500
                )
            )
            
            content = response.text
            return json.loads(content)
            
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse Google response as JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Google API call failed: {e}")
            return None
    
    def _evaluate_segment(self, segment: HighlightSegment, context: str = "") -> Optional[Dict[str, Any]]:
        """
        Evaluate a single segment using LLM.
        
        Args:
            segment: Highlight segment to evaluate
            context: Optional context
            
        Returns:
            Evaluation result or None if failed
        """
        prompt = self._create_prompt(segment, context)
        
        for attempt in range(self.max_retries):
            try:
                if self.provider == 'openai':
                    result = self._call_openai(prompt)
                elif self.provider == 'google':
                    result = self._call_google(prompt)
                else:
                    logger.error(f"Unsupported provider: {self.provider}")
                    return None
                
                if result:
                    # Validate response structure
                    required_keys = ['engaged', 'confidence', 'primary_cues', 'reasoning']
                    if all(key in result for key in required_keys):
                        return result
                    else:
                        logger.warning(f"Invalid response structure: {result}")
                
                # Rate limiting
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.rate_limit_delay * (attempt + 1))
        
        return None
    
    def _get_context(self, segment: HighlightSegment, all_segments: List[HighlightSegment]) -> str:
        """
        Get context from surrounding segments.
        
        Args:
            segment: Current segment
            all_segments: All segments for context
            
        Returns:
            Context string
        """
        if self.context_sentences == 0:
            return ""
        
        # Find segments that are temporally close
        context_segments = []
        
        for other in all_segments:
            if other.id == segment.id:
                continue
            
            # Check if segments are close in time
            time_gap = min(
                abs(other.end_sec - segment.start_sec),
                abs(segment.end_sec - other.start_sec)
            )
            
            if time_gap <= 30:  # Within 30 seconds
                context_segments.append((time_gap, other))
        
        # Sort by proximity and take closest ones
        context_segments.sort(key=lambda x: x[0])
        context_segments = context_segments[:self.context_sentences]
        
        if not context_segments:
            return ""
        
        context_texts = [seg[1].transcript[:100] + "..." for seg in context_segments]
        return " | ".join(context_texts)
    
    def rerank_segments(self, segments: List[HighlightSegment]) -> List[HighlightSegment]:
        """
        Re-rank segments using LLM evaluation.
        
        Args:
            segments: List of highlight segments to re-rank
            
        Returns:
            Filtered and re-ranked list of segments
        """
        if not self.enabled:
            logger.info("LLM re-ranking disabled, returning original segments")
            return segments
        
        if not segments:
            return segments
        
        logger.info(f"Re-ranking {len(segments)} segments using {self.provider}")
        
        # Filter segments by duration first
        eligible_segments = [
            seg for seg in segments 
            if seg.duration <= self.max_segment_duration
        ]
        
        if len(eligible_segments) < len(segments):
            logger.info(f"Filtered {len(segments)} to {len(eligible_segments)} segments by duration")
        
        evaluated_segments = []
        
        for i, segment in enumerate(eligible_segments):
            logger.info(f"Evaluating segment {i+1}/{len(eligible_segments)}")
            
            # Get context
            context = self._get_context(segment, eligible_segments)
            
            # Evaluate segment
            evaluation = self._evaluate_segment(segment, context)
            
            if evaluation:
                # Check if segment passes LLM filter
                engaged = evaluation.get('engaged', False)
                confidence = evaluation.get('confidence', 0.0)
                
                if engaged and confidence >= self.confidence_threshold:
                    # Update segment with LLM evaluation
                    segment.llm_evaluation = evaluation
                    segment.llm_confidence = confidence
                    
                    # Update cues with LLM findings
                    llm_cues = evaluation.get('primary_cues', [])
                    segment.cues.extend(llm_cues)
                    segment.cues = list(set(segment.cues))  # Remove duplicates
                    
                    evaluated_segments.append(segment)
                    
                    logger.debug(f"Segment passed LLM filter: confidence={confidence:.3f}")
                else:
                    logger.debug(f"Segment filtered out: engaged={engaged}, confidence={confidence:.3f}")
            else:
                logger.warning(f"Failed to evaluate segment {segment.id}")
        
        # Re-rank by LLM confidence
        evaluated_segments.sort(key=lambda x: x.llm_confidence, reverse=True)
        
        logger.info(f"LLM re-ranking completed: {len(evaluated_segments)} segments retained")
        
        return evaluated_segments
