"""
Engagement Scoring Module

Implements the engagement scoring algorithm with feature normalization,
weighted fusion, and temporal smoothing.
"""

import logging
from typing import Dict, Any, List

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler

logger = logging.getLogger(__name__)


class EngagementScorer:
    """
    Calculates engagement scores using weighted feature fusion
    and temporal smoothing.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize engagement scorer with configuration.
        
        Args:
            config: Configuration dictionary with scoring parameters
        """
        self.config = config
        self.weights = config.get('weights', {})
        self.smoothing_config = config.get('smoothing', {})
        self.normalization_method = config.get('normalization', {}).get('method', 'minmax')
        
        # Default weights if not specified
        self.default_weights = {
            'emotion': 0.30,
            'sentiment_delta': 0.15,
            'rhetorical': 0.10,
            'topic_shift': 0.20,
            'keyword_salience': 0.05,
            'laughter': 0.07,
            'applause': 0.05,
            'prosody': 0.05,
            'clap_similarity': 0.03
        }
        
        # Merge with provided weights
        for key, default_value in self.default_weights.items():
            if key not in self.weights:
                self.weights[key] = default_value
        
        # Normalize weights to sum to 1
        total_weight = sum(self.weights.values())
        if total_weight > 0:
            self.weights = {k: v / total_weight for k, v in self.weights.items()}
        
        logger.info(f"Initialized scorer with weights: {self.weights}")
        
    def normalize_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize feature columns to [0, 1] range.
        
        Args:
            df: DataFrame with feature columns
            
        Returns:
            DataFrame with normalized features
        """
        feature_columns = [
            'emotion_score', 'sentiment_volatility', 'rhetorical_score',
            'topic_shift_score', 'keyword_salience', 'laughter_score',
            'applause_score', 'prosody_score', 'clap_similarity'
        ]
        
        # Filter to existing columns
        existing_features = [col for col in feature_columns if col in df.columns]
        
        if not existing_features:
            logger.warning("No feature columns found for normalization")
            return df
        
        logger.info(f"Normalizing {len(existing_features)} features using {self.normalization_method}")
        
        df_normalized = df.copy()
        
        # Select normalization method
        if self.normalization_method == 'minmax':
            scaler = MinMaxScaler()
        elif self.normalization_method == 'zscore':
            scaler = StandardScaler()
        elif self.normalization_method == 'robust':
            scaler = RobustScaler()
        else:
            logger.warning(f"Unknown normalization method: {self.normalization_method}, using minmax")
            scaler = MinMaxScaler()
        
        # Normalize each feature
        for feature in existing_features:
            feature_values = df[feature].values.reshape(-1, 1)
            
            # Handle edge cases
            if len(np.unique(feature_values)) == 1:
                # All values are the same
                df_normalized[f'{feature}_norm'] = 0.0
            else:
                try:
                    normalized_values = scaler.fit_transform(feature_values).flatten()
                    # Ensure values are in [0, 1] range for minmax
                    if self.normalization_method == 'minmax':
                        normalized_values = np.clip(normalized_values, 0, 1)
                    df_normalized[f'{feature}_norm'] = normalized_values
                except Exception as e:
                    logger.warning(f"Error normalizing {feature}: {e}")
                    df_normalized[f'{feature}_norm'] = 0.0
        
        return df_normalized
    
    def calculate_weighted_score(self, df: pd.DataFrame) -> np.ndarray:
        """
        Calculate weighted engagement scores using normalized features.
        
        Args:
            df: DataFrame with normalized feature columns
            
        Returns:
            Array of weighted engagement scores
        """
        logger.info("Calculating weighted engagement scores")
        
        # Map feature names to normalized column names
        feature_mapping = {
            'emotion': 'emotion_score_norm',
            'sentiment_delta': 'sentiment_volatility_norm',
            'rhetorical': 'rhetorical_score_norm',
            'topic_shift': 'topic_shift_score_norm',
            'keyword_salience': 'keyword_salience_norm',
            'laughter': 'laughter_score_norm',
            'applause': 'applause_score_norm',
            'prosody': 'prosody_score_norm',
            'clap_similarity': 'clap_similarity_norm'
        }
        
        # Calculate weighted sum
        weighted_scores = np.zeros(len(df))
        
        for feature_key, weight in self.weights.items():
            column_name = feature_mapping.get(feature_key)
            
            if column_name and column_name in df.columns:
                feature_values = df[column_name].values
                weighted_scores += weight * feature_values
                logger.debug(f"Added {feature_key} with weight {weight:.3f}")
            else:
                logger.warning(f"Feature {feature_key} ({column_name}) not found, skipping")
        
        # Ensure scores are in [0, 1] range
        weighted_scores = np.clip(weighted_scores, 0, 1)
        
        return weighted_scores
    
    def apply_temporal_smoothing(self, scores: np.ndarray) -> np.ndarray:
        """
        Apply temporal smoothing using exponential moving average.
        
        Args:
            scores: Array of raw engagement scores
            
        Returns:
            Array of smoothed engagement scores
        """
        alpha = self.smoothing_config.get('alpha', 0.4)
        window_size = self.smoothing_config.get('window_size', 3)
        
        logger.info(f"Applying temporal smoothing with alpha={alpha}, window={window_size}")
        
        if len(scores) <= 1:
            return scores
        
        smoothed_scores = np.zeros_like(scores)
        smoothed_scores[0] = scores[0]
        
        # Apply exponential moving average
        for i in range(1, len(scores)):
            smoothed_scores[i] = alpha * scores[i] + (1 - alpha) * smoothed_scores[i-1]
        
        # Optional: Apply additional window-based smoothing
        if window_size > 1:
            window_smoothed = np.zeros_like(smoothed_scores)
            
            for i in range(len(smoothed_scores)):
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(smoothed_scores), i + window_size // 2 + 1)
                window_smoothed[i] = np.mean(smoothed_scores[start_idx:end_idx])
            
            smoothed_scores = window_smoothed
        
        return smoothed_scores
    
    def calculate_engagement_scores(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate complete engagement scores with normalization and smoothing.
        
        Args:
            df: DataFrame with extracted features
            
        Returns:
            DataFrame with engagement scores and intermediate results
        """
        logger.info("Starting engagement score calculation")
        
        # Normalize features
        df_with_norm = self.normalize_features(df)
        
        # Calculate weighted scores
        raw_scores = self.calculate_weighted_score(df_with_norm)
        df_with_norm['raw_engagement_score'] = raw_scores
        
        # Apply temporal smoothing
        smoothed_scores = self.apply_temporal_smoothing(raw_scores)
        df_with_norm['engagement_score'] = smoothed_scores
        
        # Store individual feature contributions for debugging
        feature_contributions = {}
        feature_mapping = {
            'emotion': 'emotion_score_norm',
            'sentiment_delta': 'sentiment_volatility_norm',
            'rhetorical': 'rhetorical_score_norm',
            'topic_shift': 'topic_shift_score_norm',
            'keyword_salience': 'keyword_salience_norm',
            'laughter': 'laughter_score_norm',
            'applause': 'applause_score_norm',
            'prosody': 'prosody_score_norm',
            'clap_similarity': 'clap_similarity_norm'
        }
        
        for feature_key, weight in self.weights.items():
            column_name = feature_mapping.get(feature_key)
            if column_name and column_name in df_with_norm.columns:
                contribution = weight * df_with_norm[column_name].values
                feature_contributions[f'{feature_key}_contribution'] = contribution
        
        # Add contributions to DataFrame
        for contrib_name, contrib_values in feature_contributions.items():
            df_with_norm[contrib_name] = contrib_values
        
        # Calculate statistics
        score_stats = {
            'mean_score': np.mean(smoothed_scores),
            'std_score': np.std(smoothed_scores),
            'min_score': np.min(smoothed_scores),
            'max_score': np.max(smoothed_scores),
            'median_score': np.median(smoothed_scores)
        }
        
        logger.info(f"Engagement score statistics: {score_stats}")
        
        return df_with_norm
    
    def get_score_statistics(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Get statistical summary of engagement scores.
        
        Args:
            df: DataFrame with engagement scores
            
        Returns:
            Dictionary of score statistics
        """
        if 'engagement_score' not in df.columns:
            return {}
        
        scores = df['engagement_score'].values
        
        return {
            'mean': float(np.mean(scores)),
            'std': float(np.std(scores)),
            'min': float(np.min(scores)),
            'max': float(np.max(scores)),
            'median': float(np.median(scores)),
            'q25': float(np.percentile(scores, 25)),
            'q75': float(np.percentile(scores, 75)),
            'count': len(scores)
        }
