"""
Data Loading Module

Handles loading and validation of Whisper transcripts and raw audio files.
Supports various input formats and provides standardized data structures.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

import librosa
import pandas as pd
import soundfile as sf
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)


class WhisperSegment(BaseModel):
    """Pydantic model for Whisper transcript segments."""
    
    start: float = Field(..., ge=0, description="Start time in seconds")
    end: float = Field(..., ge=0, description="End time in seconds") 
    text: str = Field(..., min_length=1, description="Transcript text")
    
    @validator('end')
    def end_after_start(cls, v, values):
        if 'start' in values and v <= values['start']:
            raise ValueError('End time must be after start time')
        return v


class WhisperTranscript(BaseModel):
    """Pydantic model for complete Whisper transcript."""
    
    segments: List[WhisperSegment] = Field(..., min_items=1)
    language: Optional[str] = None
    duration: Optional[float] = Field(None, ge=0)
    
    @validator('duration')
    def validate_duration(cls, v, values):
        if v is not None and 'segments' in values:
            max_end = max(seg.end for seg in values['segments'])
            if v < max_end:
                logger.warning(f"Duration {v} is less than max segment end {max_end}")
        return v


class AudioData(BaseModel):
    """Container for audio data and metadata."""
    
    audio: Any  # numpy array
    sample_rate: int = Field(..., gt=0)
    duration: float = Field(..., gt=0)
    channels: int = Field(default=1, ge=1)
    file_path: str


class DataLoader:
    """
    Loads and validates Whisper transcripts and audio files.
    
    Supports multiple audio formats and provides standardized output
    for downstream processing.
    """
    
    def __init__(self, target_sample_rate: int = 16000):
        """
        Initialize DataLoader.
        
        Args:
            target_sample_rate: Target sample rate for audio resampling
        """
        self.target_sample_rate = target_sample_rate
        self.supported_audio_formats = {'.wav', '.mp3', '.flac', '.m4a', '.ogg'}
        
    def load_transcript(self, transcript_path: Union[str, Path]) -> WhisperTranscript:
        """
        Load and validate Whisper transcript from JSON file.
        
        Args:
            transcript_path: Path to Whisper transcript JSON file
            
        Returns:
            Validated WhisperTranscript object
            
        Raises:
            FileNotFoundError: If transcript file doesn't exist
            ValueError: If transcript format is invalid
            json.JSONDecodeError: If JSON is malformed
        """
        transcript_path = Path(transcript_path)
        
        if not transcript_path.exists():
            raise FileNotFoundError(f"Transcript file not found: {transcript_path}")
            
        logger.info(f"Loading transcript from {transcript_path}")
        
        try:
            with open(transcript_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # Handle different Whisper output formats
            if 'segments' not in data:
                if isinstance(data, list):
                    # Direct segments list
                    data = {'segments': data}
                else:
                    raise ValueError("Invalid transcript format: missing 'segments' key")
                    
            transcript = WhisperTranscript(**data)
            logger.info(f"Loaded {len(transcript.segments)} segments")
            
            return transcript
            
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"Invalid JSON in transcript file: {e}")
        except Exception as e:
            raise ValueError(f"Error parsing transcript: {e}")
    
    def load_audio(self, audio_path: Union[str, Path]) -> AudioData:
        """
        Load and preprocess audio file.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            AudioData object with loaded audio and metadata
            
        Raises:
            FileNotFoundError: If audio file doesn't exist
            ValueError: If audio format is unsupported
            RuntimeError: If audio loading fails
        """
        audio_path = Path(audio_path)
        
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
        if audio_path.suffix.lower() not in self.supported_audio_formats:
            raise ValueError(f"Unsupported audio format: {audio_path.suffix}")
            
        logger.info(f"Loading audio from {audio_path}")
        
        try:
            # Load audio with librosa for consistent handling
            audio, sample_rate = librosa.load(
                str(audio_path),
                sr=self.target_sample_rate,
                mono=True
            )
            
            duration = len(audio) / sample_rate
            
            logger.info(f"Loaded audio: {duration:.2f}s at {sample_rate}Hz")
            
            return AudioData(
                audio=audio,
                sample_rate=sample_rate,
                duration=duration,
                channels=1,
                file_path=str(audio_path)
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to load audio file {audio_path}: {e}")
    
    def validate_alignment(self, transcript: WhisperTranscript, 
                          audio: AudioData, tolerance: float = 5.0) -> bool:
        """
        Validate that transcript and audio durations are aligned.
        
        Args:
            transcript: Loaded transcript
            audio: Loaded audio data
            tolerance: Maximum allowed difference in seconds
            
        Returns:
            True if aligned within tolerance
            
        Raises:
            ValueError: If alignment is outside tolerance
        """
        transcript_duration = max(seg.end for seg in transcript.segments)
        audio_duration = audio.duration
        
        diff = abs(transcript_duration - audio_duration)
        
        if diff > tolerance:
            raise ValueError(
                f"Duration mismatch: transcript={transcript_duration:.2f}s, "
                f"audio={audio_duration:.2f}s, diff={diff:.2f}s > {tolerance}s"
            )
            
        logger.info(f"Duration alignment OK: diff={diff:.2f}s")
        return True
    
    def load_data_pair(self, transcript_path: Union[str, Path], 
                      audio_path: Union[str, Path]) -> tuple[WhisperTranscript, AudioData]:
        """
        Load and validate transcript-audio pair.
        
        Args:
            transcript_path: Path to transcript JSON
            audio_path: Path to audio file
            
        Returns:
            Tuple of (transcript, audio_data)
        """
        transcript = self.load_transcript(transcript_path)
        audio = self.load_audio(audio_path)
        
        # Validate alignment
        self.validate_alignment(transcript, audio)
        
        return transcript, audio
