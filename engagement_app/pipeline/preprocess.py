"""
Preprocessing Module

Handles splitting Whisper segments into sentences and interpolating timestamps
proportionally by character count for more granular analysis.
"""

import logging
import re
from typing import List, Tuple, Optional

import pandas as pd
import spacy
from spacy.lang.en import English

from .load_data import WhisperTranscript, WhisperSegment

logger = logging.getLogger(__name__)


class TranscriptPreprocessor:
    """
    Preprocesses Whisper transcripts by splitting segments into sentences
    and interpolating timestamps proportionally.
    """
    
    def __init__(self, spacy_model: str = "en_core_web_sm"):
        """
        Initialize preprocessor with spaCy model.
        
        Args:
            spacy_model: Name of spaCy model to use for sentence segmentation
        """
        self.spacy_model = spacy_model
        self._nlp = None
        
    @property
    def nlp(self):
        """Lazy load spaCy model."""
        if self._nlp is None:
            try:
                self._nlp = spacy.load(self.spacy_model)
                logger.info(f"Loaded spaCy model: {self.spacy_model}")
            except OSError:
                logger.warning(f"spaCy model {self.spacy_model} not found, using basic English")
                self._nlp = English()
                self._nlp.add_pipe('sentencizer')
        return self._nlp
    
    def clean_text(self, text: str) -> str:
        """
        Clean transcript text by removing artifacts and normalizing.
        
        Args:
            text: Raw transcript text
            
        Returns:
            Cleaned text
        """
        # Remove common Whisper artifacts
        text = re.sub(r'\[.*?\]', '', text)  # Remove [MUSIC], [APPLAUSE], etc.
        text = re.sub(r'\(.*?\)', '', text)  # Remove (inaudible), etc.
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # Fix common transcription issues
        text = re.sub(r'\b([a-z])\s+([a-z])\b', r'\1\2', text)  # Fix "a b c" -> "abc"
        
        return text
    
    def split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences using spaCy.
        
        Args:
            text: Input text to split
            
        Returns:
            List of sentence strings
        """
        if not text.strip():
            return []
            
        doc = self.nlp(text)
        sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]
        
        # Filter out very short sentences (likely artifacts)
        sentences = [s for s in sentences if len(s) > 3]
        
        return sentences
    
    def interpolate_timestamps(self, segment: WhisperSegment, 
                             sentences: List[str]) -> List[Tuple[float, float]]:
        """
        Interpolate timestamps for sentences within a segment proportionally
        by character count.
        
        Args:
            segment: Original Whisper segment
            sentences: List of sentences from the segment
            
        Returns:
            List of (start_time, end_time) tuples for each sentence
        """
        if not sentences:
            return []
            
        if len(sentences) == 1:
            return [(segment.start, segment.end)]
        
        # Calculate character positions
        total_chars = sum(len(s) for s in sentences)
        if total_chars == 0:
            # Equal distribution if no characters
            duration_per_sentence = (segment.end - segment.start) / len(sentences)
            return [
                (segment.start + i * duration_per_sentence,
                 segment.start + (i + 1) * duration_per_sentence)
                for i in range(len(sentences))
            ]
        
        # Proportional distribution by character count
        segment_duration = segment.end - segment.start
        timestamps = []
        current_time = segment.start
        
        for i, sentence in enumerate(sentences):
            char_ratio = len(sentence) / total_chars
            sentence_duration = segment_duration * char_ratio
            
            # Ensure minimum duration of 0.1 seconds
            sentence_duration = max(sentence_duration, 0.1)
            
            end_time = current_time + sentence_duration
            
            # Adjust last sentence to match segment end exactly
            if i == len(sentences) - 1:
                end_time = segment.end
                
            timestamps.append((current_time, end_time))
            current_time = end_time
            
        return timestamps
    
    def process_segment(self, segment: WhisperSegment, 
                       segment_id: int) -> List[dict]:
        """
        Process a single Whisper segment into sentence-level data.
        
        Args:
            segment: Whisper segment to process
            segment_id: Unique identifier for the segment
            
        Returns:
            List of sentence dictionaries
        """
        # Clean the text
        cleaned_text = self.clean_text(segment.text)
        
        if not cleaned_text:
            logger.warning(f"Empty text after cleaning in segment {segment_id}")
            return []
        
        # Split into sentences
        sentences = self.split_into_sentences(cleaned_text)
        
        if not sentences:
            logger.warning(f"No sentences found in segment {segment_id}")
            return []
        
        # Interpolate timestamps
        timestamps = self.interpolate_timestamps(segment, sentences)
        
        # Create sentence records
        sentence_records = []
        for i, (sentence, (start_time, end_time)) in enumerate(zip(sentences, timestamps)):
            record = {
                'segment_id': segment_id,
                'sentence_id': i,
                'start_sec': start_time,
                'end_sec': end_time,
                'duration': end_time - start_time,
                'text': sentence,
                'original_start': segment.start,
                'original_end': segment.end
            }
            sentence_records.append(record)
            
        return sentence_records
    
    def process_transcript(self, transcript: WhisperTranscript, 
                          episode_id: str) -> pd.DataFrame:
        """
        Process complete transcript into sentence-level DataFrame.
        
        Args:
            transcript: Whisper transcript to process
            episode_id: Unique episode identifier
            
        Returns:
            DataFrame with columns: [episode_id, segment_id, sentence_id, 
                                   start_sec, end_sec, duration, text]
        """
        logger.info(f"Processing transcript with {len(transcript.segments)} segments")
        
        all_sentences = []
        
        for segment_idx, segment in enumerate(transcript.segments):
            try:
                sentence_records = self.process_segment(segment, segment_idx)
                all_sentences.extend(sentence_records)
            except Exception as e:
                logger.error(f"Error processing segment {segment_idx}: {e}")
                continue
        
        if not all_sentences:
            raise ValueError("No valid sentences found in transcript")
        
        # Create DataFrame
        df = pd.DataFrame(all_sentences)
        df['episode_id'] = episode_id
        
        # Reorder columns
        df = df[['episode_id', 'segment_id', 'sentence_id', 'start_sec', 
                'end_sec', 'duration', 'text', 'original_start', 'original_end']]
        
        # Add global sentence ID
        df['global_sentence_id'] = range(len(df))
        
        logger.info(f"Processed {len(df)} sentences from {len(transcript.segments)} segments")
        
        # Validation
        self._validate_processed_data(df)
        
        return df
    
    def _validate_processed_data(self, df: pd.DataFrame) -> None:
        """
        Validate processed sentence data for consistency.
        
        Args:
            df: Processed sentence DataFrame
            
        Raises:
            ValueError: If validation fails
        """
        # Check for negative durations
        negative_durations = df[df['duration'] <= 0]
        if not negative_durations.empty:
            raise ValueError(f"Found {len(negative_durations)} sentences with non-positive duration")
        
        # Check for overlapping timestamps within segments
        for segment_id in df['segment_id'].unique():
            segment_df = df[df['segment_id'] == segment_id].sort_values('sentence_id')
            
            for i in range(len(segment_df) - 1):
                current_end = segment_df.iloc[i]['end_sec']
                next_start = segment_df.iloc[i + 1]['start_sec']
                
                if current_end > next_start + 0.01:  # Small tolerance for floating point
                    logger.warning(f"Overlapping timestamps in segment {segment_id}")
        
        # Check for empty text
        empty_text = df[df['text'].str.strip() == '']
        if not empty_text.empty:
            logger.warning(f"Found {len(empty_text)} sentences with empty text")
        
        logger.info("Data validation completed successfully")
