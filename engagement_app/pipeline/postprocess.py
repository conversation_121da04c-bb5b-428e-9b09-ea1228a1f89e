"""
Postprocessing Module

Handles highlight generation including candidate selection, boundary expansion,
segment merging, and ranking.
"""

import logging
import uuid
from typing import List, Dict, Any, Tuple

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


class HighlightSegment:
    """Represents a highlight segment with metadata."""
    
    def __init__(self, start_sec: float, end_sec: float, sentences: List[Dict[str, Any]]):
        """
        Initialize highlight segment.
        
        Args:
            start_sec: Start time in seconds
            end_sec: End time in seconds
            sentences: List of sentence dictionaries in this segment
        """
        self.id = str(uuid.uuid4())
        self.start_sec = start_sec
        self.end_sec = end_sec
        self.duration = end_sec - start_sec
        self.sentences = sentences
        
        # Calculate aggregate metrics
        self.max_engagement_score = max(s['engagement_score'] for s in sentences)
        self.mean_engagement_score = np.mean([s['engagement_score'] for s in sentences])
        self.transcript = ' '.join(s['text'] for s in sentences)
        
        # Extract engagement cues
        self.cues = self._extract_cues()
        
        # Calculate ranking score
        self.ranking_score = self._calculate_ranking_score()
    
    def _extract_cues(self) -> List[str]:
        """Extract engagement cues from sentences."""
        cues = []
        
        for sentence in self.sentences:
            # Check for high individual feature scores
            if sentence.get('emotion_score', 0) > 0.7:
                cues.append('high_emotion')
            if sentence.get('laughter_score', 0) > 0.5:
                cues.append('laughter')
            if sentence.get('applause_score', 0) > 0.5:
                cues.append('applause')
            if sentence.get('rhetorical_score', 0) > 0.5:
                cues.append('rhetorical_engagement')
            if sentence.get('topic_shift_score', 0) > 0.6:
                cues.append('topic_shift')
            if sentence.get('prosody_score', 0) > 0.7:
                cues.append('prosodic_emphasis')
            if sentence.get('sentiment_volatility', 0) > 0.6:
                cues.append('sentiment_volatility')
        
        # Remove duplicates and return
        return list(set(cues))
    
    def _calculate_ranking_score(self, duration_weight: float = 0.3) -> float:
        """Calculate ranking score for this segment."""
        return self.max_engagement_score * (1 + duration_weight * np.log(self.duration + 1))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert segment to dictionary format."""
        return {
            'id': self.id,
            'start_sec': self.start_sec,
            'end_sec': self.end_sec,
            'duration': self.duration,
            'cues': self.cues,
            'confidence': self.max_engagement_score,
            'transcript': self.transcript,
            'engagement_score': self.max_engagement_score,
            'feature_scores': self._get_feature_scores()
        }
    
    def _get_feature_scores(self) -> Dict[str, float]:
        """Get aggregated feature scores for this segment."""
        if not self.sentences:
            return {}
        
        feature_keys = [
            'emotion_score', 'sentiment_volatility', 'rhetorical_score',
            'topic_shift_score', 'keyword_salience', 'laughter_score',
            'applause_score', 'prosody_score', 'clap_similarity'
        ]
        
        feature_scores = {}
        for key in feature_keys:
            values = [s.get(key, 0) for s in self.sentences]
            feature_scores[key.replace('_score', '')] = float(np.mean(values))
        
        return feature_scores


class HighlightGenerator:
    """
    Generates highlight segments from engagement scores using
    candidate selection, boundary expansion, and merging.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize highlight generator with configuration.
        
        Args:
            config: Configuration dictionary with highlight parameters
        """
        self.config = config
        self.selection_threshold_std = config.get('selection_threshold_std', 1.0)
        self.min_duration_sec = config.get('min_duration_sec', 30)
        self.max_duration_sec = config.get('max_duration_sec', 180)
        self.merge_gap_sec = config.get('merge_gap_sec', 8)
        self.boundary_extension_sentences = config.get('boundary_extension_sentences', 1)
        self.top_k = config.get('top_k', 10)
        self.ranking_duration_weight = config.get('ranking_duration_weight', 0.3)
        
        logger.info(f"Initialized highlight generator with config: {self.config}")
    
    def identify_candidates(self, df: pd.DataFrame) -> List[int]:
        """
        Identify candidate sentences for highlights based on engagement scores.
        
        Args:
            df: DataFrame with engagement scores
            
        Returns:
            List of sentence indices that are candidates
        """
        if 'engagement_score' not in df.columns:
            raise ValueError("DataFrame must contain 'engagement_score' column")
        
        scores = df['engagement_score'].values
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        
        threshold = mean_score + self.selection_threshold_std * std_score
        
        candidates = df[df['engagement_score'] > threshold].index.tolist()
        
        logger.info(f"Identified {len(candidates)} candidate sentences "
                   f"(threshold: {threshold:.3f}, mean: {mean_score:.3f}, std: {std_score:.3f})")
        
        return candidates
    
    def group_consecutive_candidates(self, candidates: List[int], df: pd.DataFrame) -> List[List[int]]:
        """
        Group consecutive candidate sentences into potential segments.
        
        Args:
            candidates: List of candidate sentence indices
            df: DataFrame with sentence data
            
        Returns:
            List of groups, where each group is a list of consecutive indices
        """
        if not candidates:
            return []
        
        candidates = sorted(candidates)
        groups = []
        current_group = [candidates[0]]
        
        for i in range(1, len(candidates)):
            current_idx = candidates[i]
            prev_idx = candidates[i-1]
            
            # Check if sentences are consecutive or very close in time
            current_start = df.loc[current_idx, 'start_sec']
            prev_end = df.loc[prev_idx, 'end_sec']
            
            if (current_idx == prev_idx + 1) or (current_start - prev_end <= self.merge_gap_sec):
                current_group.append(current_idx)
            else:
                groups.append(current_group)
                current_group = [current_idx]
        
        groups.append(current_group)
        
        logger.info(f"Grouped {len(candidates)} candidates into {len(groups)} potential segments")
        
        return groups
    
    def expand_boundaries(self, group: List[int], df: pd.DataFrame) -> List[int]:
        """
        Expand segment boundaries by adding adjacent sentences.
        
        Args:
            group: List of sentence indices in the group
            df: DataFrame with sentence data
            
        Returns:
            Expanded list of sentence indices
        """
        if not group:
            return group
        
        min_idx = min(group)
        max_idx = max(group)
        
        # Expand backwards
        for _ in range(self.boundary_extension_sentences):
            if min_idx > 0:
                min_idx -= 1
        
        # Expand forwards
        for _ in range(self.boundary_extension_sentences):
            if max_idx < len(df) - 1:
                max_idx += 1
        
        # Return all indices in the expanded range
        expanded = list(range(min_idx, max_idx + 1))
        
        return expanded
    
    def create_highlight_segment(self, indices: List[int], df: pd.DataFrame) -> HighlightSegment:
        """
        Create a highlight segment from sentence indices.
        
        Args:
            indices: List of sentence indices
            df: DataFrame with sentence data
            
        Returns:
            HighlightSegment object
        """
        if not indices:
            raise ValueError("Cannot create segment from empty indices")
        
        # Get sentence data
        sentences = []
        for idx in sorted(indices):
            sentence_data = df.loc[idx].to_dict()
            sentences.append(sentence_data)
        
        # Calculate segment boundaries
        start_sec = min(df.loc[idx, 'start_sec'] for idx in indices)
        end_sec = max(df.loc[idx, 'end_sec'] for idx in indices)
        
        return HighlightSegment(start_sec, end_sec, sentences)
    
    def filter_by_duration(self, segments: List[HighlightSegment]) -> List[HighlightSegment]:
        """
        Filter segments by duration constraints.
        
        Args:
            segments: List of highlight segments
            
        Returns:
            Filtered list of segments
        """
        filtered = []
        
        for segment in segments:
            if segment.duration < self.min_duration_sec:
                logger.debug(f"Segment {segment.id} too short: {segment.duration:.1f}s < {self.min_duration_sec}s")
                continue
            
            if segment.duration > self.max_duration_sec:
                logger.debug(f"Segment {segment.id} too long: {segment.duration:.1f}s > {self.max_duration_sec}s")
                continue
            
            filtered.append(segment)
        
        logger.info(f"Filtered {len(segments)} segments to {len(filtered)} by duration")
        
        return filtered
    
    def merge_overlapping_segments(self, segments: List[HighlightSegment]) -> List[HighlightSegment]:
        """
        Merge segments that are close together or overlapping.
        
        Args:
            segments: List of highlight segments
            
        Returns:
            List of merged segments
        """
        if len(segments) <= 1:
            return segments
        
        # Sort by start time
        segments = sorted(segments, key=lambda x: x.start_sec)
        merged = [segments[0]]
        
        for current in segments[1:]:
            last_merged = merged[-1]
            
            # Check if segments should be merged
            gap = current.start_sec - last_merged.end_sec
            
            if gap <= self.merge_gap_sec:
                # Merge segments
                combined_sentences = last_merged.sentences + current.sentences
                merged_segment = HighlightSegment(
                    last_merged.start_sec,
                    current.end_sec,
                    combined_sentences
                )
                merged[-1] = merged_segment
                logger.debug(f"Merged segments with gap {gap:.1f}s")
            else:
                merged.append(current)
        
        logger.info(f"Merged {len(segments)} segments to {len(merged)}")
        
        return merged
    
    def rank_segments(self, segments: List[HighlightSegment]) -> List[HighlightSegment]:
        """
        Rank segments by engagement score and duration.
        
        Args:
            segments: List of highlight segments
            
        Returns:
            List of segments sorted by ranking score (descending)
        """
        # Update ranking scores with current duration weight
        for segment in segments:
            segment.ranking_score = segment._calculate_ranking_score(self.ranking_duration_weight)
        
        # Sort by ranking score (descending)
        ranked = sorted(segments, key=lambda x: x.ranking_score, reverse=True)
        
        logger.info(f"Ranked {len(segments)} segments")
        
        return ranked
    
    def generate_highlights(self, df: pd.DataFrame) -> List[HighlightSegment]:
        """
        Generate highlight segments from engagement scores.
        
        Args:
            df: DataFrame with engagement scores and sentence data
            
        Returns:
            List of top-K ranked highlight segments
        """
        logger.info("Starting highlight generation")
        
        # Step 1: Identify candidates
        candidates = self.identify_candidates(df)
        
        if not candidates:
            logger.warning("No candidates found for highlights")
            return []
        
        # Step 2: Group consecutive candidates
        groups = self.group_consecutive_candidates(candidates, df)
        
        # Step 3: Expand boundaries and create segments
        segments = []
        for group in groups:
            try:
                expanded_group = self.expand_boundaries(group, df)
                segment = self.create_highlight_segment(expanded_group, df)
                segments.append(segment)
            except Exception as e:
                logger.warning(f"Error creating segment from group {group}: {e}")
                continue
        
        # Step 4: Filter by duration
        segments = self.filter_by_duration(segments)
        
        # Step 5: Merge overlapping segments
        segments = self.merge_overlapping_segments(segments)
        
        # Step 6: Rank segments
        segments = self.rank_segments(segments)
        
        # Step 7: Return top-K
        top_segments = segments[:self.top_k]
        
        logger.info(f"Generated {len(top_segments)} highlight segments")
        
        return top_segments
