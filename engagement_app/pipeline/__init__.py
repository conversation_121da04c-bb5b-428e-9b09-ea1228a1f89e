"""
Audience Engagement Detection Pipeline

A production-ready system for detecting audience engagement from long-form audio content
using unsupervised methods. Processes OpenAI Whisper transcripts and raw audio files
to output validated JSON containing highlight segments with timestamps, engagement cues,
and confidence scores.
"""

__version__ = "1.0.0"
__author__ = "MLOps Engineering Team"

from .load_data import DataLoader
from .preprocess import TranscriptPreprocessor
from .features import FeatureExtractor
from .scoring import EngagementScorer
from .postprocess import HighlightGenerator
from .llm_rerank import LLMReranker
from .export_json import JSONExporter

__all__ = [
    "DataLoader",
    "TranscriptPreprocessor", 
    "FeatureExtractor",
    "EngagementScorer",
    "HighlightGenerator",
    "LLMReranker",
    "JSONExporter"
]
