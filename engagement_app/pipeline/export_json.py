"""
JSON Export Module

Handles exporting highlight segments to validated JSON format
according to the defined schema.
"""

import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any, Optional

import jsonschema

from .postprocess import HighlightSegment

logger = logging.getLogger(__name__)


class JSONExporter:
    """
    Exports highlight segments to validated JSON format.
    
    Handles schema validation, metadata generation, and file output.
    """
    
    def __init__(self, config: Dict[str, Any], schema_path: Optional[str] = None):
        """
        Initialize JSON exporter.
        
        Args:
            config: Configuration dictionary
            schema_path: Path to JSON schema file for validation
        """
        self.config = config
        self.version = config.get('version', 'engage-v1.0')
        self.include_debug_info = config.get('include_debug_info', True)
        self.pretty_print = config.get('pretty_print', True)
        self.validate_schema = config.get('validate_schema', True)
        
        # Load schema for validation
        self.schema = None
        if schema_path and self.validate_schema:
            self._load_schema(schema_path)
        
        logger.info(f"Initialized JSON exporter with version {self.version}")
    
    def _load_schema(self, schema_path: str):
        """
        Load JSON schema for validation.
        
        Args:
            schema_path: Path to schema file
        """
        try:
            schema_file = Path(schema_path)
            if schema_file.exists():
                with open(schema_file, 'r') as f:
                    self.schema = json.load(f)
                logger.info(f"Loaded JSON schema from {schema_path}")
            else:
                logger.warning(f"Schema file not found: {schema_path}")
        except Exception as e:
            logger.error(f"Failed to load schema: {e}")
            self.schema = None
    
    def _generate_metadata(self, segments: List[HighlightSegment], 
                          processing_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate processing metadata.
        
        Args:
            segments: List of highlight segments
            processing_stats: Processing statistics
            
        Returns:
            Metadata dictionary
        """
        if not segments:
            total_duration = 0
            segments_analyzed = processing_stats.get('segments_analyzed', 0)
        else:
            total_duration = processing_stats.get('total_duration_sec', 0)
            segments_analyzed = processing_stats.get('segments_analyzed', len(segments))
        
        # Determine features used
        features_used = [
            'emotion_scores', 'sentiment_volatility', 'rhetorical_patterns',
            'topic_shifts', 'keyword_salience'
        ]
        
        # Add audio features if available
        if any(hasattr(seg, 'laughter_score') for seg in segments):
            features_used.extend(['laughter_detection', 'applause_detection', 'prosodic_emphasis'])
        
        # Check if LLM re-ranking was used
        llm_reranked = any(hasattr(seg, 'llm_evaluation') for seg in segments)
        if llm_reranked:
            features_used.append('llm_reranking')
        
        metadata = {
            'total_duration_sec': total_duration,
            'segments_analyzed': segments_analyzed,
            'llm_reranked': llm_reranked,
            'features_used': features_used,
            'processing_time_sec': processing_stats.get('processing_time_sec', 0)
        }
        
        return metadata
    
    def _convert_segment_to_dict(self, segment: HighlightSegment) -> Dict[str, Any]:
        """
        Convert highlight segment to dictionary format.
        
        Args:
            segment: Highlight segment to convert
            
        Returns:
            Dictionary representation of segment
        """
        segment_dict = segment.to_dict()
        
        # Ensure all required fields are present
        required_fields = ['id', 'start_sec', 'end_sec', 'duration', 'cues', 'confidence', 'transcript']
        for field in required_fields:
            if field not in segment_dict:
                logger.warning(f"Missing required field '{field}' in segment {segment.id}")
                segment_dict[field] = None
        
        # Add optional debug information
        if self.include_debug_info:
            if hasattr(segment, 'llm_evaluation'):
                segment_dict['llm_evaluation'] = segment.llm_evaluation
            
            if hasattr(segment, 'ranking_score'):
                segment_dict['ranking_score'] = segment.ranking_score
        
        return segment_dict
    
    def create_output_json(self, episode_id: str, segments: List[HighlightSegment],
                          processing_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create complete JSON output structure.
        
        Args:
            episode_id: Unique episode identifier
            segments: List of highlight segments
            processing_stats: Processing statistics
            
        Returns:
            Complete JSON structure
        """
        # Generate timestamp
        generated_utc = datetime.now(timezone.utc).isoformat()
        
        # Generate metadata
        metadata = self._generate_metadata(segments, processing_stats)
        
        # Convert segments
        segments_data = [self._convert_segment_to_dict(seg) for seg in segments]
        
        # Create complete structure
        output_json = {
            'episode_id': episode_id,
            'generated_utc': generated_utc,
            'version': self.version,
            'processing_metadata': metadata,
            'segments': segments_data
        }
        
        return output_json
    
    def validate_json(self, json_data: Dict[str, Any]) -> bool:
        """
        Validate JSON against schema.
        
        Args:
            json_data: JSON data to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not self.schema or not self.validate_schema:
            logger.info("Schema validation skipped")
            return True
        
        try:
            jsonschema.validate(json_data, self.schema)
            logger.info("JSON validation successful")
            return True
        except jsonschema.ValidationError as e:
            logger.error(f"JSON validation failed: {e.message}")
            logger.error(f"Failed at path: {' -> '.join(str(p) for p in e.absolute_path)}")
            return False
        except Exception as e:
            logger.error(f"Validation error: {e}")
            return False
    
    def export_to_file(self, json_data: Dict[str, Any], output_path: str) -> bool:
        """
        Export JSON data to file.
        
        Args:
            json_data: JSON data to export
            output_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                if self.pretty_print:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                else:
                    json.dump(json_data, f, ensure_ascii=False)
            
            logger.info(f"Exported JSON to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export JSON to {output_path}: {e}")
            return False
    
    def export_highlights(self, episode_id: str, segments: List[HighlightSegment],
                         processing_stats: Dict[str, Any], output_path: str) -> bool:
        """
        Complete export workflow: create JSON, validate, and save to file.
        
        Args:
            episode_id: Unique episode identifier
            segments: List of highlight segments
            processing_stats: Processing statistics
            output_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Exporting {len(segments)} highlights for episode {episode_id}")
        
        # Create JSON structure
        json_data = self.create_output_json(episode_id, segments, processing_stats)
        
        # Validate JSON
        if not self.validate_json(json_data):
            logger.error("JSON validation failed, aborting export")
            return False
        
        # Export to file
        success = self.export_to_file(json_data, output_path)
        
        if success:
            logger.info(f"Successfully exported highlights to {output_path}")
            
            # Log summary statistics
            total_duration = sum(seg.duration for seg in segments)
            avg_confidence = sum(seg.max_engagement_score for seg in segments) / len(segments) if segments else 0
            
            logger.info(f"Export summary: {len(segments)} segments, "
                       f"total duration: {total_duration:.1f}s, "
                       f"avg confidence: {avg_confidence:.3f}")
        
        return success
    
    def create_sample_output(self, output_path: str) -> bool:
        """
        Create a sample output file for demonstration.
        
        Args:
            output_path: Output file path
            
        Returns:
            True if successful
        """
        # Create sample data
        sample_segments = [
            {
                'id': 'sample-segment-1',
                'start_sec': 120.5,
                'end_sec': 165.2,
                'duration': 44.7,
                'cues': ['high_emotion', 'laughter', 'rhetorical_engagement'],
                'confidence': 0.87,
                'transcript': 'This is a sample transcript segment that demonstrates high audience engagement with emotional content and humor.',
                'engagement_score': 0.87,
                'feature_scores': {
                    'emotion': 0.85,
                    'sentiment_volatility': 0.45,
                    'rhetorical': 0.72,
                    'topic_shift': 0.23,
                    'keyword_salience': 0.56,
                    'laughter': 0.78,
                    'applause': 0.12,
                    'prosody': 0.68,
                    'clap_similarity': 0.34
                }
            }
        ]
        
        sample_json = {
            'episode_id': 'demo_episode_001',
            'generated_utc': datetime.now(timezone.utc).isoformat(),
            'version': self.version,
            'processing_metadata': {
                'total_duration_sec': 3600.0,
                'segments_analyzed': 450,
                'llm_reranked': False,
                'features_used': [
                    'emotion_scores', 'sentiment_volatility', 'rhetorical_patterns',
                    'topic_shifts', 'keyword_salience', 'laughter_detection',
                    'applause_detection', 'prosodic_emphasis'
                ],
                'processing_time_sec': 285.6
            },
            'segments': sample_segments
        }
        
        return self.export_to_file(sample_json, output_path)
