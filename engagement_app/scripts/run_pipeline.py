#!/usr/bin/env python3
"""
Audience Engagement Detection Pipeline CLI

Main script for running the complete engagement detection pipeline
from Whisper transcripts and audio files to validated JSON output.
"""

import argparse
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

import yaml
import structlog

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from pipeline import (
    DataLoader, TranscriptPreprocessor, FeatureExtractor,
    EngagementScorer, HighlightGenerator, LLMReranker, JSONExporter
)


def setup_logging(log_level: str = "INFO", log_file: str = None) -> None:
    """
    Setup structured logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
    """
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            *([logging.FileHandler(log_file)] if log_file else [])
        ]
    )


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load pipeline configuration from YAML file.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        logging.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logging.error(f"Failed to load configuration: {e}")
        sys.exit(1)


def validate_inputs(args: argparse.Namespace) -> None:
    """
    Validate input arguments and file paths.
    
    Args:
        args: Parsed command line arguments
    """
    # Check required files exist
    if not Path(args.transcript_path).exists():
        logging.error(f"Transcript file not found: {args.transcript_path}")
        sys.exit(1)
    
    if args.audio_path and not Path(args.audio_path).exists():
        logging.error(f"Audio file not found: {args.audio_path}")
        sys.exit(1)
    
    if not Path(args.config).exists():
        logging.error(f"Configuration file not found: {args.config}")
        sys.exit(1)
    
    # Create output directory
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)


def run_pipeline(args: argparse.Namespace) -> bool:
    """
    Run the complete engagement detection pipeline.
    
    Args:
        args: Parsed command line arguments
        
    Returns:
        True if successful, False otherwise
    """
    logger = structlog.get_logger()
    start_time = time.time()
    
    try:
        # Load configuration
        config = load_config(args.config)
        
        # Initialize pipeline components
        logger.info("Initializing pipeline components")
        
        data_loader = DataLoader()
        preprocessor = TranscriptPreprocessor(config['models']['spacy_model'])
        feature_extractor = FeatureExtractor(config['features'])
        scorer = EngagementScorer(config['scoring'])
        highlight_generator = HighlightGenerator(config['highlights'])
        
        # Initialize optional components
        llm_reranker = None
        if args.use_llm and config['llm']['enabled']:
            llm_reranker = LLMReranker(config['llm'])
        
        # Initialize JSON exporter
        schema_path = Path(__file__).parent.parent / "schemas" / "highlights_schema.json"
        json_exporter = JSONExporter(config['output'], str(schema_path))
        
        # Step 1: Load data
        logger.info("Loading transcript and audio data")
        transcript = data_loader.load_transcript(args.transcript_path)
        
        audio_data = None
        if args.audio_path:
            audio_data = data_loader.load_audio(args.audio_path)
            data_loader.validate_alignment(transcript, audio_data)
        else:
            logger.warning("No audio file provided, audio features will be disabled")
        
        # Step 2: Preprocess transcript
        logger.info("Preprocessing transcript")
        df = preprocessor.process_transcript(transcript, args.episode_id)
        
        # Step 3: Extract features
        logger.info("Extracting engagement features")
        df = feature_extractor.extract_features(df, audio_data)
        
        # Step 4: Calculate engagement scores
        logger.info("Calculating engagement scores")
        df = scorer.calculate_engagement_scores(df)
        
        # Step 5: Generate highlights
        logger.info("Generating highlight segments")
        highlights = highlight_generator.generate_highlights(df)
        
        if not highlights:
            logger.warning("No highlights generated")
            return False
        
        # Step 6: Optional LLM re-ranking
        if llm_reranker and args.use_llm:
            logger.info("Applying LLM re-ranking")
            highlights = llm_reranker.rerank_segments(highlights)
        
        # Apply top-k filtering
        if args.top_k and args.top_k < len(highlights):
            highlights = highlights[:args.top_k]
            logger.info(f"Filtered to top {args.top_k} highlights")
        
        # Step 7: Export results
        processing_time = time.time() - start_time
        processing_stats = {
            'total_duration_sec': audio_data.duration if audio_data else 0,
            'segments_analyzed': len(df),
            'processing_time_sec': processing_time
        }
        
        output_path = Path(args.output_dir) / f"{args.episode_id}_highlights.json"
        success = json_exporter.export_highlights(
            args.episode_id, highlights, processing_stats, str(output_path)
        )
        
        if success:
            logger.info(f"Pipeline completed successfully in {processing_time:.1f}s")
            logger.info(f"Generated {len(highlights)} highlights")
            logger.info(f"Output saved to: {output_path}")
            return True
        else:
            logger.error("Failed to export results")
            return False
            
    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Audience Engagement Detection Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with transcript only
  python run_pipeline.py --episode_id "podcast_ep_123" \\
    --transcript_path "../data/transcripts/episode.json" \\
    --output_dir "../outputs/"

  # Full pipeline with audio and LLM re-ranking
  python run_pipeline.py --episode_id "podcast_ep_123" \\
    --audio_path "../data/raw_audio/episode.wav" \\
    --transcript_path "../data/transcripts/episode.json" \\
    --output_dir "../outputs/" \\
    --use_llm --llm_provider "openai" --top_k 15
        """
    )
    
    # Required arguments
    parser.add_argument(
        '--episode_id', required=True,
        help='Unique identifier for the episode'
    )
    parser.add_argument(
        '--transcript_path', required=True,
        help='Path to Whisper transcript JSON file'
    )
    parser.add_argument(
        '--output_dir', required=True,
        help='Output directory for results'
    )
    
    # Optional arguments
    parser.add_argument(
        '--audio_path',
        help='Path to audio file (optional, enables audio features)'
    )
    parser.add_argument(
        '--config', default='engagement_app/config/pipeline_config.yaml',
        help='Path to pipeline configuration file'
    )
    parser.add_argument(
        '--use_llm', action='store_true',
        help='Enable LLM re-ranking of highlights'
    )
    parser.add_argument(
        '--llm_provider', choices=['openai', 'google'], default='openai',
        help='LLM provider for re-ranking'
    )
    parser.add_argument(
        '--top_k', type=int,
        help='Maximum number of highlights to return'
    )
    parser.add_argument(
        '--log_level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO',
        help='Logging level'
    )
    parser.add_argument(
        '--log_file',
        help='Optional log file path'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    
    # Validate inputs
    validate_inputs(args)
    
    # Run pipeline
    success = run_pipeline(args)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
