#!/usr/bin/env python3
"""
Setup Script for Engagement Detection Pipeline

Handles installation of dependencies, model downloads, and environment setup.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a shell command with error handling."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"  Command: {command}")
        print(f"  Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is 3.10+."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print(f"✗ Python 3.10+ required, found {version.major}.{version.minor}")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_requirements():
    """Install Python requirements."""
    requirements_file = Path(__file__).parent.parent / "requirements.txt"
    if not requirements_file.exists():
        print("✗ requirements.txt not found")
        return False
    
    return run_command(
        f"pip install -r {requirements_file}",
        "Installing Python dependencies"
    )


def download_spacy_model():
    """Download spaCy English model."""
    return run_command(
        "python -m spacy download en_core_web_sm",
        "Downloading spaCy English model"
    )


def download_nltk_data():
    """Download NLTK VADER lexicon."""
    return run_command(
        "python -c \"import nltk; nltk.download('vader_lexicon', quiet=True)\"",
        "Downloading NLTK VADER lexicon"
    )


def create_directories():
    """Create necessary directories."""
    base_dir = Path(__file__).parent.parent
    directories = [
        base_dir / "data" / "raw_audio",
        base_dir / "data" / "transcripts",
        base_dir / "outputs",
        base_dir / "logs",
        base_dir / ".cache"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True


def test_imports():
    """Test that key imports work."""
    print("\nTesting imports...")
    
    test_imports = [
        ("spacy", "spaCy"),
        ("transformers", "Transformers"),
        ("torch", "PyTorch"),
        ("librosa", "Librosa"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("sklearn", "Scikit-learn"),
        ("nltk", "NLTK"),
        ("jsonschema", "JSON Schema"),
        ("yaml", "PyYAML")
    ]
    
    failed_imports = []
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError as e:
            print(f"✗ {name}: {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n✗ Failed to import: {', '.join(failed_imports)}")
        return False
    
    print("✓ All imports successful")
    return True


def test_spacy_model():
    """Test spaCy model loading."""
    print("\nTesting spaCy model...")
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        doc = nlp("This is a test sentence.")
        print(f"✓ spaCy model loaded successfully ({len(list(doc.sents))} sentences detected)")
        return True
    except Exception as e:
        print(f"✗ spaCy model test failed: {e}")
        return False


def test_nltk_data():
    """Test NLTK VADER data."""
    print("\nTesting NLTK VADER...")
    try:
        from nltk.sentiment import SentimentIntensityAnalyzer
        sia = SentimentIntensityAnalyzer()
        scores = sia.polarity_scores("This is a great test!")
        print(f"✓ NLTK VADER working (compound score: {scores['compound']:.3f})")
        return True
    except Exception as e:
        print(f"✗ NLTK VADER test failed: {e}")
        return False


def create_sample_files():
    """Create sample configuration and data files."""
    base_dir = Path(__file__).parent.parent
    
    # Create sample transcript
    sample_transcript = {
        "segments": [
            {
                "start": 0.0,
                "end": 5.2,
                "text": "Welcome to our podcast about technology and innovation."
            },
            {
                "start": 5.2,
                "end": 12.8,
                "text": "Today we're discussing the future of artificial intelligence and its impact on society."
            },
            {
                "start": 12.8,
                "end": 18.5,
                "text": "This is really exciting stuff that everyone should know about!"
            }
        ],
        "language": "en",
        "duration": 18.5
    }
    
    sample_file = base_dir / "data" / "transcripts" / "sample_transcript.json"
    try:
        import json
        with open(sample_file, 'w') as f:
            json.dump(sample_transcript, f, indent=2)
        print(f"✓ Created sample transcript: {sample_file}")
    except Exception as e:
        print(f"✗ Failed to create sample transcript: {e}")
        return False
    
    return True


def main():
    """Main setup function."""
    print("=== Engagement Detection Pipeline Setup ===")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n✗ Setup failed during dependency installation")
        sys.exit(1)
    
    # Download models
    if not download_spacy_model():
        print("\n⚠ Warning: spaCy model download failed")
        print("  You can try manually: python -m spacy download en_core_web_sm")
    
    if not download_nltk_data():
        print("\n⚠ Warning: NLTK data download failed")
        print("  You can try manually in Python: import nltk; nltk.download('vader_lexicon')")
    
    # Create directories
    if not create_directories():
        print("\n✗ Setup failed during directory creation")
        sys.exit(1)
    
    # Test imports
    if not test_imports():
        print("\n✗ Setup failed during import testing")
        print("  Try reinstalling requirements: pip install -r requirements.txt")
        sys.exit(1)
    
    # Test models
    test_spacy_model()
    test_nltk_data()
    
    # Create sample files
    create_sample_files()
    
    print("\n=== Setup Complete ===")
    print("✓ All dependencies installed")
    print("✓ Models downloaded")
    print("✓ Directories created")
    print("✓ Sample files created")
    
    print("\nNext steps:")
    print("1. Set API keys (optional, for LLM re-ranking):")
    print("   export OPENAI_API_KEY='your-key'")
    print("   export GOOGLE_API_KEY='your-key'")
    print("\n2. Test the pipeline:")
    print("   cd engagement_app")
    print("   python scripts/run_pipeline.py --episode_id 'test' \\")
    print("     --transcript_path 'data/transcripts/sample_transcript.json' \\")
    print("     --output_dir 'outputs/'")
    print("\n3. Run benchmarks:")
    print("   python scripts/benchmark.py --durations 5 15")


if __name__ == '__main__':
    main()
