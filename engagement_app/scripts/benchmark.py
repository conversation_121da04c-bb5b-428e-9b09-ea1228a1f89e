#!/usr/bin/env python3
"""
Performance Benchmarking Script

Benchmarks the engagement detection pipeline performance
with various input sizes and configurations.
"""

import argparse
import json
import logging
import time
import sys
from pathlib import Path
from typing import Dict, List, Any

import numpy as np
import pandas as pd

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from pipeline import (
    DataLoader, TranscriptPreprocessor, FeatureExtractor,
    EngagementScorer, HighlightGenerator
)


def create_synthetic_transcript(duration_minutes: int, segments_per_minute: int = 15) -> Dict[str, Any]:
    """
    Create synthetic Whisper transcript for benchmarking.
    
    Args:
        duration_minutes: Duration in minutes
        segments_per_minute: Number of segments per minute
        
    Returns:
        Synthetic transcript dictionary
    """
    total_segments = duration_minutes * segments_per_minute
    segment_duration = 60.0 / segments_per_minute
    
    # Sample sentences for variety
    sample_sentences = [
        "Welcome to our podcast about technology and innovation.",
        "Today we're discussing the future of artificial intelligence.",
        "This is really exciting stuff that everyone should know about.",
        "What do you think about these recent developments?",
        "I'm absolutely fascinated by how quickly things are changing.",
        "Let me tell you about an incredible experience I had recently.",
        "The implications of this technology are truly mind-blowing.",
        "Are you ready for what comes next in this space?",
        "This could completely revolutionize how we work and live.",
        "I can't believe how far we've come in just a few years."
    ]
    
    segments = []
    for i in range(total_segments):
        start_time = i * segment_duration
        end_time = start_time + segment_duration + np.random.uniform(-0.5, 0.5)
        
        # Select random sentence with some variation
        base_sentence = np.random.choice(sample_sentences)
        if np.random.random() < 0.3:  # Add excitement
            base_sentence += " This is amazing!"
        if np.random.random() < 0.2:  # Add questions
            base_sentence += " Don't you agree?"
        
        segments.append({
            "start": start_time,
            "end": end_time,
            "text": base_sentence
        })
    
    return {
        "segments": segments,
        "language": "en",
        "duration": duration_minutes * 60.0
    }


def create_synthetic_audio(duration_seconds: float, sample_rate: int = 16000) -> np.ndarray:
    """
    Create synthetic audio data for benchmarking.
    
    Args:
        duration_seconds: Duration in seconds
        sample_rate: Audio sample rate
        
    Returns:
        Synthetic audio array
    """
    num_samples = int(duration_seconds * sample_rate)
    
    # Create realistic audio with varying energy levels
    audio = np.random.randn(num_samples) * 0.1
    
    # Add some periodic energy spikes (simulating speech)
    for i in range(0, num_samples, sample_rate // 2):  # Every 0.5 seconds
        if np.random.random() < 0.7:  # 70% chance of speech
            end_idx = min(i + sample_rate // 4, num_samples)  # 0.25 second bursts
            audio[i:end_idx] *= np.random.uniform(2, 5)
    
    return audio


def benchmark_component(component_name: str, func, *args, **kwargs) -> Dict[str, float]:
    """
    Benchmark a single pipeline component.
    
    Args:
        component_name: Name of the component
        func: Function to benchmark
        *args, **kwargs: Arguments for the function
        
    Returns:
        Timing results
    """
    print(f"Benchmarking {component_name}...")
    
    # Warm-up run
    try:
        func(*args, **kwargs)
    except Exception as e:
        print(f"Warm-up failed for {component_name}: {e}")
    
    # Actual benchmark
    start_time = time.time()
    try:
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"{component_name}: {execution_time:.2f}s")
        
        return {
            "component": component_name,
            "execution_time": execution_time,
            "success": True,
            "error": None
        }
    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"{component_name} FAILED: {e}")
        
        return {
            "component": component_name,
            "execution_time": execution_time,
            "success": False,
            "error": str(e)
        }


def run_full_pipeline_benchmark(duration_minutes: int, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run complete pipeline benchmark.
    
    Args:
        duration_minutes: Audio duration in minutes
        config: Pipeline configuration
        
    Returns:
        Benchmark results
    """
    print(f"\n=== Benchmarking {duration_minutes}-minute audio ===")
    
    # Create synthetic data
    print("Creating synthetic data...")
    transcript_data = create_synthetic_transcript(duration_minutes)
    audio_data = create_synthetic_audio(duration_minutes * 60.0)
    
    # Initialize components
    preprocessor = TranscriptPreprocessor()
    feature_extractor = FeatureExtractor(config.get('features', {}))
    scorer = EngagementScorer(config.get('scoring', {}))
    highlight_generator = HighlightGenerator(config.get('highlights', {}))
    
    # Convert to required format
    from pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData
    
    segments = [WhisperSegment(**seg) for seg in transcript_data['segments']]
    transcript = WhisperTranscript(segments=segments, duration=transcript_data['duration'])
    
    audio = AudioData(
        audio=audio_data,
        sample_rate=16000,
        duration=duration_minutes * 60.0,
        channels=1,
        file_path="synthetic.wav"
    )
    
    # Benchmark each component
    results = []
    
    # Preprocessing
    result = benchmark_component(
        "Preprocessing",
        preprocessor.process_transcript,
        transcript, f"benchmark_{duration_minutes}min"
    )
    results.append(result)
    
    if not result['success']:
        return {"duration_minutes": duration_minutes, "results": results, "total_time": sum(r['execution_time'] for r in results)}
    
    df = preprocessor.process_transcript(transcript, f"benchmark_{duration_minutes}min")
    
    # Feature extraction
    result = benchmark_component(
        "Feature Extraction",
        feature_extractor.extract_features,
        df, audio
    )
    results.append(result)
    
    if not result['success']:
        return {"duration_minutes": duration_minutes, "results": results, "total_time": sum(r['execution_time'] for r in results)}
    
    df_features = feature_extractor.extract_features(df, audio)
    
    # Scoring
    result = benchmark_component(
        "Engagement Scoring",
        scorer.calculate_engagement_scores,
        df_features
    )
    results.append(result)
    
    if not result['success']:
        return {"duration_minutes": duration_minutes, "results": results, "total_time": sum(r['execution_time'] for r in results)}
    
    df_scores = scorer.calculate_engagement_scores(df_features)
    
    # Highlight generation
    result = benchmark_component(
        "Highlight Generation",
        highlight_generator.generate_highlights,
        df_scores
    )
    results.append(result)
    
    total_time = sum(r['execution_time'] for r in results)
    processing_ratio = total_time / (duration_minutes * 60.0)
    
    print(f"Total processing time: {total_time:.2f}s")
    print(f"Processing ratio: {processing_ratio:.3f} (target: <0.125)")
    print(f"Segments processed: {len(df)}")
    
    return {
        "duration_minutes": duration_minutes,
        "results": results,
        "total_time": total_time,
        "processing_ratio": processing_ratio,
        "segments_processed": len(df),
        "target_met": processing_ratio < 0.125
    }


def main():
    """Main benchmarking function."""
    parser = argparse.ArgumentParser(description="Benchmark engagement detection pipeline")
    parser.add_argument('--durations', nargs='+', type=int, default=[5, 15, 30, 60],
                       help='Audio durations to test (minutes)')
    parser.add_argument('--config', default='../config/pipeline_config.yaml',
                       help='Pipeline configuration file')
    parser.add_argument('--output', default='benchmark_results.json',
                       help='Output file for results')
    
    args = parser.parse_args()
    
    # Load configuration
    import yaml
    try:
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"Failed to load config: {e}")
        config = {}
    
    # Run benchmarks
    all_results = []
    
    for duration in args.durations:
        try:
            result = run_full_pipeline_benchmark(duration, config)
            all_results.append(result)
        except Exception as e:
            print(f"Benchmark failed for {duration} minutes: {e}")
            all_results.append({
                "duration_minutes": duration,
                "error": str(e),
                "success": False
            })
    
    # Save results
    with open(args.output, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # Print summary
    print("\n=== BENCHMARK SUMMARY ===")
    for result in all_results:
        if result.get('success', True):
            duration = result['duration_minutes']
            total_time = result['total_time']
            ratio = result['processing_ratio']
            target_met = "✓" if result['target_met'] else "✗"
            print(f"{duration:2d}min: {total_time:6.1f}s (ratio: {ratio:.3f}) {target_met}")
        else:
            print(f"{result['duration_minutes']:2d}min: FAILED - {result.get('error', 'Unknown error')}")
    
    print(f"\nResults saved to: {args.output}")


if __name__ == '__main__':
    main()
