#!/usr/bin/env python3
"""
Installation Validation Script

Validates that the engagement detection pipeline is properly installed
and all components are working correctly.
"""

import json
import sys
import tempfile
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))


def test_basic_imports():
    """Test that all required modules can be imported."""
    print("Testing basic imports...")
    
    try:
        from pipeline import (
            DataLoader, TranscriptPreprocessor, FeatureExtractor,
            EngagementScorer, HighlightGenerator, LLMReranker, JSONExporter
        )
        print("✓ Pipeline modules imported successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to import pipeline modules: {e}")
        return False


def test_data_loading():
    """Test data loading functionality."""
    print("\nTesting data loading...")
    
    try:
        from pipeline.load_data import DataLoader, WhisperTranscript, WhisperSegment
        
        # Create sample transcript data
        segments = [
            WhisperSegment(start=0.0, end=5.0, text="Hello world"),
            WhisperSegment(start=5.0, end=10.0, text="This is a test")
        ]
        transcript = WhisperTranscript(segments=segments, duration=10.0)
        
        print(f"✓ Created transcript with {len(transcript.segments)} segments")
        return True
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False


def test_preprocessing():
    """Test preprocessing functionality."""
    print("\nTesting preprocessing...")
    
    try:
        from pipeline.preprocess import TranscriptPreprocessor
        from pipeline.load_data import WhisperTranscript, WhisperSegment
        
        # Create test data
        segments = [
            WhisperSegment(start=0.0, end=5.0, text="Hello world! How are you?"),
            WhisperSegment(start=5.0, end=10.0, text="This is a test. Really exciting!")
        ]
        transcript = WhisperTranscript(segments=segments, duration=10.0)
        
        # Test preprocessing
        preprocessor = TranscriptPreprocessor()
        df = preprocessor.process_transcript(transcript, "test_episode")
        
        print(f"✓ Preprocessed {len(df)} sentences")
        return True
    except Exception as e:
        print(f"✗ Preprocessing test failed: {e}")
        return False


def test_feature_extraction():
    """Test feature extraction with mocked models."""
    print("\nTesting feature extraction...")
    
    try:
        import pandas as pd
        import numpy as np
        from unittest.mock import patch
        from pipeline.features import FeatureExtractor
        
        # Create test DataFrame
        df = pd.DataFrame({
            'text': ["Hello world!", "How are you?", "This is great!"],
            'start_sec': [0.0, 2.0, 4.0],
            'end_sec': [2.0, 4.0, 6.0]
        })
        
        # Mock configuration
        config = {
            'text': {'rhetorical_patterns': [r'\?', r'!']},
            'audio': {'sample_rate': 16000}
        }
        
        extractor = FeatureExtractor(config)
        
        # Mock the heavy ML models
        with patch.object(extractor.text_extractor, 'extract_emotion_scores', return_value=np.array([0.5, 0.8, 0.3])), \
             patch.object(extractor.text_extractor, 'extract_sentiment_volatility', return_value=np.array([0.0, 0.6, 0.2])), \
             patch.object(extractor.text_extractor, 'extract_rhetorical_features', return_value=np.array([0.2, 0.9, 0.4])), \
             patch.object(extractor.text_extractor, 'extract_topic_shifts', return_value=np.array([0.0, 0.3, 0.1])), \
             patch.object(extractor.text_extractor, 'extract_keyword_salience', return_value=np.array([0.4, 0.7, 0.5])):
            
            df_features = extractor.extract_features(df)
            
            # Check that features were added
            feature_columns = [
                'emotion_score', 'sentiment_volatility', 'rhetorical_score',
                'topic_shift_score', 'keyword_salience'
            ]
            
            if all(col in df_features.columns for col in feature_columns):
                print(f"✓ Extracted {len(feature_columns)} text features")
                return True
            else:
                print("✗ Missing feature columns")
                return False
                
    except Exception as e:
        print(f"✗ Feature extraction test failed: {e}")
        return False


def test_scoring():
    """Test engagement scoring."""
    print("\nTesting engagement scoring...")
    
    try:
        import pandas as pd
        import numpy as np
        from pipeline.scoring import EngagementScorer
        
        # Create test DataFrame with features
        df = pd.DataFrame({
            'emotion_score': [0.5, 0.8, 0.3],
            'sentiment_volatility': [0.2, 0.6, 0.1],
            'rhetorical_score': [0.3, 0.9, 0.2],
            'topic_shift_score': [0.1, 0.4, 0.7],
            'keyword_salience': [0.4, 0.7, 0.3],
            'laughter_score': [0.0, 0.5, 0.0],
            'applause_score': [0.0, 0.0, 0.0],
            'prosody_score': [0.2, 0.8, 0.3],
            'clap_similarity': [0.0, 0.0, 0.0]
        })
        
        config = {
            'weights': {
                'emotion': 0.30, 'sentiment_delta': 0.15, 'rhetorical': 0.10,
                'topic_shift': 0.20, 'keyword_salience': 0.05, 'laughter': 0.07,
                'applause': 0.05, 'prosody': 0.05, 'clap_similarity': 0.03
            },
            'smoothing': {'alpha': 0.4},
            'normalization': {'method': 'minmax'}
        }
        
        scorer = EngagementScorer(config)
        df_scores = scorer.calculate_engagement_scores(df)
        
        if 'engagement_score' in df_scores.columns:
            print(f"✓ Calculated engagement scores for {len(df_scores)} segments")
            return True
        else:
            print("✗ Engagement scores not calculated")
            return False
            
    except Exception as e:
        print(f"✗ Scoring test failed: {e}")
        return False


def test_json_export():
    """Test JSON export functionality."""
    print("\nTesting JSON export...")
    
    try:
        from pipeline.export_json import JSONExporter
        from pipeline.postprocess import HighlightSegment
        
        # Create mock highlight
        mock_sentences = [
            {'text': 'This is amazing!', 'engagement_score': 0.9}
        ]
        highlights = [HighlightSegment(10.0, 25.0, mock_sentences)]
        
        config = {
            'version': 'engage-v1.0',
            'include_debug_info': True,
            'validate_schema': False  # Skip schema validation for this test
        }
        
        exporter = JSONExporter(config)
        
        processing_stats = {
            'total_duration_sec': 60.0,
            'segments_analyzed': 10,
            'processing_time_sec': 5.0
        }
        
        json_data = exporter.create_output_json("test", highlights, processing_stats)
        
        # Validate basic structure
        required_fields = ['episode_id', 'generated_utc', 'version', 'segments']
        if all(field in json_data for field in required_fields):
            print(f"✓ Generated JSON with {len(json_data['segments'])} segments")
            return True
        else:
            print("✗ JSON structure validation failed")
            return False
            
    except Exception as e:
        print(f"✗ JSON export test failed: {e}")
        return False


def test_schema_validation():
    """Test JSON schema validation."""
    print("\nTesting schema validation...")
    
    try:
        import jsonschema
        
        # Load schema
        schema_path = Path(__file__).parent.parent / "schemas" / "highlights_schema.json"
        if not schema_path.exists():
            print("✗ Schema file not found")
            return False
        
        with open(schema_path, 'r') as f:
            schema = json.load(f)
        
        # Load sample output
        sample_path = Path(__file__).parent.parent.parent / "outputs" / "highlights_demo.json"
        if not sample_path.exists():
            print("✗ Sample output file not found")
            return False
        
        with open(sample_path, 'r') as f:
            sample_data = json.load(f)
        
        # Validate
        jsonschema.validate(sample_data, schema)
        print("✓ Schema validation successful")
        return True
        
    except jsonschema.ValidationError as e:
        print(f"✗ Schema validation failed: {e.message}")
        return False
    except Exception as e:
        print(f"✗ Schema validation test failed: {e}")
        return False


def main():
    """Run all validation tests."""
    print("=== Engagement Detection Pipeline Validation ===\n")
    
    tests = [
        test_basic_imports,
        test_data_loading,
        test_preprocessing,
        test_feature_extraction,
        test_scoring,
        test_json_export,
        test_schema_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("  → This test failure may indicate missing dependencies or configuration issues")
    
    print(f"\n=== Validation Summary ===")
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ All tests passed! The pipeline is ready to use.")
        print("\nNext steps:")
        print("1. Run the setup script: python scripts/setup.py")
        print("2. Test with sample data: python scripts/run_pipeline.py --help")
        return True
    else:
        print("✗ Some tests failed. Please check the installation.")
        print("\nTroubleshooting:")
        print("1. Run: pip install -r requirements.txt")
        print("2. Run: python scripts/setup.py")
        print("3. Check that all dependencies are properly installed")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
