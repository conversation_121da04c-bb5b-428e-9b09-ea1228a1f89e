#!/usr/bin/env python3
"""
Sample Data Structure Validation

Validates the structure and content of the sample data files
without requiring heavy ML dependencies.
"""

import json
import sys
from pathlib import Path


def get_sample_data_paths():
    """Get paths to sample data files."""
    base_path = Path(__file__).parent.parent.parent / "sample" / "audio1" / "transcript"
    
    return {
        'transcript_json': base_path / "transcript.json",
        'audio_mp3': base_path / "audio.mp3",
        'transcript_txt': base_path / "transcript.txt",
        'transcript_vtt': base_path / "transcript.vtt"
    }


def test_sample_data_exists():
    """Test that all sample data files exist."""
    print("=== Testing Sample Data Existence ===")
    
    sample_paths = get_sample_data_paths()
    all_exist = True
    
    for name, path in sample_paths.items():
        if path.exists():
            size = path.stat().st_size
            print(f"✓ {name}: {path} ({size:,} bytes)")
        else:
            print(f"✗ {name}: {path} (NOT FOUND)")
            all_exist = False
    
    return all_exist


def test_transcript_json_structure():
    """Test the structure of the Whisper transcript JSON."""
    print("\n=== Testing Transcript JSON Structure ===")
    
    try:
        sample_paths = get_sample_data_paths()
        
        with open(sample_paths['transcript_json'], 'r') as f:
            data = json.load(f)
        
        print(f"✓ Successfully loaded JSON file")
        
        # Check top-level structure
        required_fields = ['text', 'segments', 'language']
        for field in required_fields:
            if field in data:
                print(f"✓ Found required field: {field}")
            else:
                print(f"✗ Missing required field: {field}")
                return False
        
        # Check segments structure
        segments = data['segments']
        print(f"✓ Found {len(segments)} segments")
        
        if len(segments) == 0:
            print("✗ No segments found")
            return False
        
        # Check first segment structure
        first_segment = segments[0]
        segment_fields = ['id', 'start', 'end', 'text']
        
        for field in segment_fields:
            if field in first_segment:
                print(f"✓ Segment has field: {field}")
            else:
                print(f"✗ Segment missing field: {field}")
                return False
        
        # Validate data types and ranges
        print(f"✓ First segment: {first_segment['start']:.1f}s - {first_segment['end']:.1f}s")
        print(f"✓ First segment text: {first_segment['text'][:100]}...")
        
        # Check duration
        if 'duration' in data:
            duration = data['duration']
            print(f"✓ Total duration: {duration:.1f} seconds ({duration/60:.1f} minutes)")
        
        # Check language
        language = data.get('language', 'unknown')
        print(f"✓ Language: {language}")
        
        # Validate segment ordering and timing
        for i in range(min(10, len(segments))):  # Check first 10 segments
            seg = segments[i]
            if seg['start'] >= seg['end']:
                print(f"✗ Invalid timing in segment {i}: start={seg['start']}, end={seg['end']}")
                return False
        
        print("✓ Segment timing validation passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to validate transcript JSON: {e}")
        return False


def test_transcript_content_analysis():
    """Analyze the content of the transcript for engagement potential."""
    print("\n=== Testing Transcript Content Analysis ===")
    
    try:
        sample_paths = get_sample_data_paths()
        
        with open(sample_paths['transcript_json'], 'r') as f:
            data = json.load(f)
        
        # Combine all segment text
        all_text = ' '.join(seg['text'] for seg in data['segments']).lower()
        
        print(f"✓ Total text length: {len(all_text):,} characters")
        
        # Check for engagement indicators
        engagement_indicators = {
            'questions': ['?', 'what', 'how', 'why', 'when', 'where', 'who'],
            'exclamations': ['!', 'amazing', 'incredible', 'fascinating', 'beautiful'],
            'second_person': ['you', 'your', 'yours'],
            'intensifiers': ['very', 'really', 'absolutely', 'extremely', 'incredibly'],
            'emotional_words': ['love', 'hate', 'excited', 'surprised', 'shocked'],
            'topic_keywords': ['artificial intelligence', 'physics', 'universe', 'technology', 'future']
        }
        
        for category, indicators in engagement_indicators.items():
            found_count = sum(1 for indicator in indicators if indicator in all_text)
            total_count = len(indicators)
            percentage = (found_count / total_count) * 100
            
            print(f"✓ {category}: {found_count}/{total_count} indicators found ({percentage:.1f}%)")
            
            if found_count > 0:
                found_indicators = [ind for ind in indicators if ind in all_text]
                print(f"  Found: {', '.join(found_indicators[:5])}{'...' if len(found_indicators) > 5 else ''}")
        
        # Check for topic diversity
        topics = [
            'physics', 'universe', 'alien', 'civilization', 'technology',
            'artificial intelligence', 'robot', 'future', 'mars', 'space',
            'quantum', 'energy', 'galaxy', 'planet', 'science'
        ]
        
        found_topics = [topic for topic in topics if topic in all_text]
        print(f"✓ Topic diversity: {len(found_topics)}/{len(topics)} topics found")
        print(f"  Topics: {', '.join(found_topics[:8])}{'...' if len(found_topics) > 8 else ''}")
        
        # Check for speaker identification
        speakers = ['michio', 'kaku', 'alex', 'friedman', 'lex']
        found_speakers = [speaker for speaker in speakers if speaker in all_text]
        if found_speakers:
            print(f"✓ Identified speakers: {', '.join(found_speakers)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed content analysis: {e}")
        return False


def test_vtt_format():
    """Test the VTT subtitle format."""
    print("\n=== Testing VTT Format ===")
    
    try:
        sample_paths = get_sample_data_paths()
        
        with open(sample_paths['transcript_vtt'], 'r') as f:
            lines = f.readlines()
        
        print(f"✓ VTT file has {len(lines)} lines")
        
        # Check VTT header
        if lines[0].strip() == 'WEBVTT':
            print("✓ Valid VTT header found")
        else:
            print("✗ Invalid VTT header")
            return False
        
        # Parse a few subtitle entries
        subtitle_count = 0
        timestamp_count = 0
        
        for line in lines[:50]:  # Check first 50 lines
            line = line.strip()
            if '-->' in line:
                timestamp_count += 1
                # Parse timestamp format: 00:00:00,000 --> 00:00:02,800
                parts = line.split(' --> ')
                if len(parts) == 2:
                    print(f"✓ Valid timestamp: {line}")
                    break
            elif line.isdigit():
                subtitle_count += 1
        
        print(f"✓ Found {subtitle_count} subtitle entries and {timestamp_count} timestamps")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed VTT format test: {e}")
        return False


def test_audio_file_basic():
    """Basic test of audio file presence and size."""
    print("\n=== Testing Audio File ===")
    
    try:
        sample_paths = get_sample_data_paths()
        audio_path = sample_paths['audio_mp3']
        
        if not audio_path.exists():
            print("✗ Audio file not found")
            return False
        
        size = audio_path.stat().st_size
        print(f"✓ Audio file exists: {size:,} bytes ({size/1024/1024:.1f} MB)")
        
        # Check file extension
        if audio_path.suffix.lower() == '.mp3':
            print("✓ Audio file has MP3 extension")
        else:
            print(f"⚠ Unexpected audio format: {audio_path.suffix}")
        
        # Estimate duration based on file size (rough estimate)
        # Typical MP3: ~1MB per minute at 128kbps
        estimated_duration_min = size / (1024 * 1024)
        print(f"✓ Estimated duration: ~{estimated_duration_min:.1f} minutes")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed audio file test: {e}")
        return False


def test_data_consistency():
    """Test consistency between different data formats."""
    print("\n=== Testing Data Consistency ===")
    
    try:
        sample_paths = get_sample_data_paths()
        
        # Load JSON data
        with open(sample_paths['transcript_json'], 'r') as f:
            json_data = json.load(f)
        
        # Load text data
        with open(sample_paths['transcript_txt'], 'r') as f:
            txt_data = f.read()
        
        # Load VTT data
        with open(sample_paths['transcript_vtt'], 'r') as f:
            vtt_data = f.read()
        
        # Compare content lengths
        json_text = json_data.get('text', '')
        
        print(f"✓ JSON full text: {len(json_text):,} characters")
        print(f"✓ TXT file: {len(txt_data):,} characters")
        print(f"✓ VTT file: {len(vtt_data):,} characters")
        
        # Check if content is similar (allowing for formatting differences)
        json_words = len(json_text.split())
        txt_words = len(txt_data.split())
        
        if abs(json_words - txt_words) / max(json_words, txt_words) < 0.1:
            print("✓ JSON and TXT content lengths are consistent")
        else:
            print(f"⚠ Content length mismatch: JSON={json_words} words, TXT={txt_words} words")
        
        # Check for key phrases in all formats
        key_phrases = ['michio kaku', 'artificial intelligence', 'kardashev scale']
        
        for phrase in key_phrases:
            in_json = phrase.lower() in json_text.lower()
            in_txt = phrase.lower() in txt_data.lower()
            in_vtt = phrase.lower() in vtt_data.lower()
            
            if in_json and in_txt and in_vtt:
                print(f"✓ Key phrase '{phrase}' found in all formats")
            else:
                print(f"⚠ Key phrase '{phrase}' missing from some formats: JSON={in_json}, TXT={in_txt}, VTT={in_vtt}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed consistency test: {e}")
        return False


def main():
    """Run all sample data validation tests."""
    print("=== Sample Data Structure Validation ===")
    print("Testing the Michio Kaku interview sample data for pipeline compatibility\n")
    
    tests = [
        ("Sample Data Existence", test_sample_data_exists),
        ("Transcript JSON Structure", test_transcript_json_structure),
        ("Transcript Content Analysis", test_transcript_content_analysis),
        ("VTT Format", test_vtt_format),
        ("Audio File Basic", test_audio_file_basic),
        ("Data Consistency", test_data_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n💥 {test_name}: ERROR - {e}")
    
    print(f"\n{'='*50}")
    print(f"VALIDATION SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SUCCESS! Sample data is valid and ready for pipeline testing.")
        print("\nThe sample data contains:")
        print("- Real Whisper transcript from Michio Kaku interview (~61 minutes)")
        print("- Rich content with high engagement potential")
        print("- Multiple format options (JSON, TXT, VTT)")
        print("- Corresponding MP3 audio file")
        print("- Diverse topics: AI, physics, universe, civilizations")
        print("- Natural conversation with questions, exclamations, and emotional content")
        print("\nThis data is perfect for testing the engagement detection pipeline!")
        
        return True
    else:
        print("\n❌ Some validation tests failed. Please check the sample data.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
