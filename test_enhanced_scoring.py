#!/usr/bin/env python3
"""
Test script for the enhanced highlights scoring system
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_enhanced_scoring():
    """Test the enhanced scoring system without OpenAI dependencies"""
    print("🧪 Testing Enhanced Highlights Scoring System")
    print("=" * 50)
    
    try:
        # Import the enhanced scorer
        from highlight_extraction.utils.scoring import HighlightsScorer
        print("✅ Successfully imported HighlightsScorer")
        
        # Initialize the scorer
        scorer = HighlightsScorer()
        print("✅ Successfully initialized HighlightsScorer")
        
        # Test 1: Q&A Detection with Semantic Analysis
        print("\n🔍 Test 1: Enhanced Q&A Detection")
        test_segments = [
            {'text': 'What is the future of artificial intelligence?', 'speaker': 'interviewer'},
            {'text': 'The future of AI is incredibly promising. We are seeing advances in machine learning, natural language processing, and computer vision that will revolutionize how we work and live.', 'speaker': 'expert'},
            {'text': 'That sounds fascinating. Can you tell me more?', 'speaker': 'interviewer'},
            {'text': 'Absolutely! AI will transform healthcare, education, and transportation in ways we can barely imagine today.', 'speaker': 'expert'}
        ]
        
        qa_scores = scorer.detect_qa_patterns(test_segments)
        print(f"   Q&A Scores: {qa_scores}")
        
        # Test 2: Enhanced Emotion Intensity with Engagement Detection
        print("\n😊 Test 2: Enhanced Emotion Intensity")
        test_texts = [
            "This is absolutely amazing and incredible!",
            "I'm really excited about this breakthrough.",
            "The results are surprisingly good.",
            "This is a normal statement."
        ]
        
        for text in test_texts:
            emotion_score = scorer.calculate_emotion_intensity(text)
            print(f"   '{text}' -> Emotion Score: {emotion_score:.3f}")
        
        # Test 3: Advanced Keyword Density
        print("\n🔑 Test 3: Advanced Keyword Density")
        test_text = "Artificial intelligence and machine learning are transforming the technology landscape with innovative AI solutions."
        keywords = ["AI", "artificial intelligence", "machine learning", "technology"]
        
        keyword_score = scorer.calculate_keyword_density(test_text, keywords)
        print(f"   Text: '{test_text}'")
        print(f"   Keywords: {keywords}")
        print(f"   Keyword Density Score: {keyword_score:.3f}")
        
        # Test 4: Novelty Scoring
        print("\n🆕 Test 4: Novelty Scoring")
        current_text = "This is a completely new and unique topic that hasn't been discussed before."
        context_texts = [
            "We talked about artificial intelligence earlier.",
            "Machine learning was mentioned in the previous segment.",
            "The discussion focused on technology trends."
        ]
        
        novelty_score = scorer.calculate_novelty_score(current_text, context_texts)
        print(f"   Current: '{current_text}'")
        print(f"   Novelty Score: {novelty_score:.3f}")
        
        # Test 5: Composite Scoring
        print("\n🎯 Test 5: Composite Scoring")
        qa_score = 0.8
        keyword_density = 0.6
        emotion_intensity = 0.7
        novelty = 0.5
        
        composite_score = scorer.calculate_composite_score(qa_score, keyword_density, emotion_intensity, novelty)
        print(f"   Q&A: {qa_score}, Keywords: {keyword_density}, Emotion: {emotion_intensity}, Novelty: {novelty}")
        print(f"   Composite Score: {composite_score:.3f}")
        
        print("\n🎉 All tests completed successfully!")
        print("✅ Enhanced scoring system is working without OpenAI dependencies")
        print("✅ Improved accuracy through multi-component analysis")
        print("✅ Self-contained and cost-effective solution")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_scoring()
    sys.exit(0 if success else 1)
