# Advanced Face Detection Dependencies
# Install these for optimal face detection accuracy

# High Accuracy Face Detection
insightface>=0.7.3          # SCRFD - High accuracy face detection
onnxruntime>=1.15.0         # Required for InsightFace (CPU)

# Fast and Reliable Face Detection
mediapipe>=0.10.0           # Google's MediaPipe - Fast and reliable

# Core dependencies (already in main requirements)
opencv-python>=4.5.0
numpy>=1.21.0

# GPU acceleration dependencies (optional but recommended)
# Uncomment the following lines for GPU acceleration:
# onnxruntime-gpu>=1.15.0   # GPU support for InsightFace
# torch>=1.9.0              # Required for CUDA detection
# For MediaPipe GPU support, ensure proper OpenGL/EGL drivers are installed
