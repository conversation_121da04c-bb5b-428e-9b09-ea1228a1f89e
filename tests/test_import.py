import sys
print(sys.path)
try:
    import engagement_app
    print("Package found at:", engagement_app.__file__)

    from engagement_app.api import EngagementDetector, QuickProcessor, quick_highlights
    print("EngagementDetector imported successfully:", EngagementDetector)
    print("QuickProcessor imported successfully:", QuickProcessor)
    print("quick_highlights imported successfully:", quick_highlights)
except ImportError as e:
    print("Import error:", str(e))