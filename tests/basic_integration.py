#!/usr/bin/env python3
"""
Basic Integration Examples for Vido Tide

This script demonstrates various ways to integrate the engagement detection
system into your projects.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from engagement_app.api import EngagementDetector, QuickProcessor, quick_highlights


def example_1_quick_start():
    """Example 1: Quick highlight extraction with minimal setup."""
    print("=== Example 1: Quick Start ===")
    
    # This is the simplest way to get highlights
    highlights = quick_highlights(
        transcript_path="tests/sample/video1/transcript/transcript.json",
        audio_path="tests/sample/video1/transcript/audio.mp3",  # Now with increased tolerance
        top_k=3
    )
    
    print(f"Found {len(highlights)} highlights:")
    for i, highlight in enumerate(highlights, 1):
        print(f"\n{i}. Highlight ({highlight['start_sec']:.1f}s - {highlight['end_sec']:.1f}s)")
        print(f"   Score: {highlight['engagement_score']:.3f}")
        print(f"   Cues: {', '.join(highlight['cues'])}")
        print(f"   Text: {highlight['transcript'][:100]}...")

def main():
    """Run all examples."""
    print("Vido Tide Integration Examples")
    print("=" * 50)
    
    try:
        example_1_quick_start()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("\nNext steps:")
        print("1. Install the package: pip install -e .")
        print("2. Try these examples with your own data")
        print("3. Customize configurations for your use case")
        print("4. Integrate into your existing pipelines")
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("Make sure you have installed the package and have sample data available")


if __name__ == "__main__":
    main()
