#!/usr/bin/env python3
"""
Question-Answer Pair Segment Extractor Task

This task creates separate video segments for complete question-answer pairs,
applying vertical video formatting and face detection to each segment.
Each Q&A pair becomes an individual short-form vertical video optimized for social media.

Core Algorithm:
1. Identify complete question-answer pairs with temporal continuity
2. Extract each Q&A pair as a separate video segment
3. Apply vertical video formatting and face detection to each segment
4. Generate individual video files with appropriate naming convention
5. Maintain existing scoring system but prioritize complete Q&A pairs
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
import ffmpeg

from .base_task import BaseTask
from .reframer import Reframer
# Updated imports to use new highlight extraction module
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from highlight_extraction.utils.question_identifier import QuestionIdentifier
from highlight_extraction.utils.iframe_utils import IFrameExtractor
from config.settings import PIPELINE_OUTPUT_DIR


class QAPairSegmentExtractor(BaseTask):
    """
    Task for extracting individual video segments for each question-answer pair
    """

    task_name = "qa_pair_segment_extractor"
    requires_gpu = False

    def __init__(self):
        super().__init__()
        self.question_identifier = QuestionIdentifier()
        self.iframe_extractor = IFrameExtractor()
        self.reframer = Reframer()

    def run(self, job_id: str, qa_highlights_result: Dict[str, Any],
            transcription_result: Dict[str, Any], video_ingestor_result: Dict[str, Any],
            params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract individual video segments for each question-answer pair

        Args:
            job_id: Unique identifier for the job
            qa_highlights_result: Results from QA highlights extractor
            transcription_result: Results from transcription engine
            video_ingestor_result: Results from video ingestor
            params: Additional parameters

        Returns:
            Task result with Q&A segment metadata and file paths
        """
        start_time = time.time()

        try:
            # Create output directory for Q&A segments
            job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            qa_segments_dir = os.path.join(job_dir, "qa_segments")
            os.makedirs(qa_segments_dir, exist_ok=True)

            # Get video path
            video_path = video_ingestor_result.get('video_path')
            if not video_path or not os.path.exists(video_path):
                raise ValueError(f"Invalid video path: {video_path}")

            # Get transcription segments from the transcript file
            transcript_path = transcription_result.get('transcript_path')
            self.logger.info(f"Transcript path: {transcript_path}")
            self.logger.info(f"Transcription result keys: {list(transcription_result.keys())}")

            if not transcript_path or not os.path.exists(transcript_path):
                raise ValueError(f"Transcript file not found: {transcript_path}")

            # Load transcript data from JSON file
            try:
                with open(transcript_path, 'r') as f:
                    transcript_data = json.load(f)
                transcript_segments = transcript_data.get('segments', [])
                self.logger.info(f"Loaded {len(transcript_segments)} transcript segments from {transcript_path}")
            except (json.JSONDecodeError, IOError) as e:
                raise ValueError(f"Failed to load transcript data: {str(e)}")

            if not transcript_segments:
                raise ValueError("No transcript segments found")

            # Get output format preference and keywords
            output_format = params.get('output_format', 'vertical')
            keywords = params.get('keywords', [])
            # Get min/max duration for Q&A segments from params, defaulting if not present
            min_segment_duration = float(params.get('min_duration', 10.0)) # Default to 10s
            max_segment_duration = float(params.get('max_duration', 60.0)) # Default to 60s


            self.logger.info(f"Starting Q&A pair segment extraction for job {job_id}")
            self.logger.info(f"Video path: {video_path}")
            self.logger.info(f"Output format: {output_format}")
            self.logger.info(f"Target keywords: {keywords}")

            # Step 1: Check if we have Q&A highlights from intelligent extractor (Q&A-first approach)
            qa_pairs = []
            approach_used = "traditional"

            if qa_highlights_result and qa_highlights_result.get('qa_first_approach'):
                self.logger.info("🎯 Using Q&A pairs from intelligent highlights extractor (Q&A-first approach)")
                # Extract Q&A pairs from intelligent highlights result
                qa_pairs = self._extract_qa_pairs_from_intelligent_highlights(qa_highlights_result, transcript_segments)
                approach_used = "qa_first"
            else:
                # Fallback: Identify complete question-answer pairs from full transcript
                self.logger.info("📺 Scanning full transcript for Q&A pairs (traditional approach)")
                qa_pairs = self._identify_complete_qa_pairs(transcript_segments, keywords, min_segment_duration, max_segment_duration)

            self.logger.info(f"Processing {len(qa_pairs)} complete Q&A pairs using {approach_used} approach")

            if not qa_pairs:
                self.logger.warning("No complete Q&A pairs found")
                return {
                    'status': 'completed',
                    'qa_segments': [],
                    'total_segments': 0,
                    'execution_time': time.time() - start_time,
                    'approach': approach_used
                }

            # Step 2: Extract I-frame timestamps for precise cutting
            iframe_timestamps = self.iframe_extractor.extract_iframe_timestamps(video_path)

            # Step 3: Process each Q&A pair into individual video segments
            qa_segments = []
            for i, qa_pair in enumerate(qa_pairs):
                segment_result = self._create_qa_segment(
                    qa_pair, i + 1, video_path, qa_segments_dir,
                    iframe_timestamps, output_format, params
                )
                if segment_result:
                    qa_segments.append(segment_result)

            self.logger.info(f"Successfully created {len(qa_segments)} Q&A video segments")

            execution_time = time.time() - start_time

            return {
                'status': 'completed',
                'qa_segments': qa_segments,
                'total_segments': len(qa_segments),
                'qa_segments_dir': qa_segments_dir,
                'execution_time': execution_time,
                'approach': approach_used
            }

        except Exception as e:
            self.logger.error(f"Error in Q&A pair segment extraction: {str(e)}")
            return {
                'status': 'failed',
                'error': str(e),
                'execution_time': time.time() - start_time
            }

    def _identify_complete_qa_pairs(self, transcript_segments: List[Dict[str, Any]],
                                   keywords: Optional[List[str]] = None,
                                   min_duration: float = 10.0,
                                   max_duration: float = 60.0) -> List[Dict[str, Any]]:
        """
        Identify complete question-answer pairs with temporal continuity

        Args:
            transcript_segments: List of transcript segments
            keywords: Target keywords for relevance scoring
            min_duration: Minimum duration for a Q&A segment
            max_duration: Maximum duration for a Q&A segment

        Returns:
            List of complete Q&A pairs with timing information
        """
        qa_pairs = []

        # Use the existing question segment identifier to find Q&A pairs
        question_segments = self.question_identifier.identify_all_question_segments(transcript_segments)

        for question_segment in question_segments:
            # Extract question and answer information
            question_idx = question_segment['question_idx']
            question_text = question_segment['question_text']
            question_start = question_segment['question_start']
            question_end = question_segment['question_end']

            # Get answer information if available
            answer_data = question_segment.get('answer_data')
            if not answer_data:
                continue

            answer_idx = answer_data['answer_idx']
            answer_text = answer_data['answer_text']
            answer_start = answer_data['answer_start']
            answer_end = answer_data['answer_end']

            # Ensure temporal continuity (answer should follow question reasonably)
            time_gap = answer_start - question_end
            if time_gap > 10.0:  # Skip if gap is too large (more than 10 seconds)
                continue

            # Calculate total duration
            total_start = question_start
            total_end = answer_end
            total_duration = total_end - total_start

            # Filter by duration
            if total_duration < min_duration or total_duration > max_duration:
                continue

            qa_pair = {
                'question_idx': question_idx,
                'question_text': question_text,
                'question_start': question_start,
                'question_end': question_end,
                'answer_idx': answer_idx,
                'answer_text': answer_text,
                'answer_start': answer_start,
                'answer_end': answer_end,
                'total_start': total_start,
                'total_end': total_end,
                'total_duration': total_duration,
                'time_gap': time_gap,
                'quality_score': question_segment.get('quality_score', 0.5)
            }

            qa_pairs.append(qa_pair)

        # Sort by quality score (highest first) and then by start time
        qa_pairs.sort(key=lambda x: (-x['quality_score'], x['total_start']))

        return qa_pairs

    def _create_qa_segment(self, qa_pair: Dict[str, Any], segment_number: int,
                          video_path: str, output_dir: str, iframe_timestamps: List[float],
                          output_format: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Create individual video segment for a Q&A pair

        Args:
            qa_pair: Q&A pair data
            segment_number: Sequential segment number
            video_path: Path to source video
            output_dir: Output directory for segments
            iframe_timestamps: List of I-frame timestamps
            output_format: Output video format (vertical, square, original)
            params: Additional parameters

        Returns:
            Segment result data or None if failed
        """
        try:
            # Generate segment filename
            segment_filename = f"qa_pair_{segment_number:03d}.mp4"
            segment_path = os.path.join(output_dir, segment_filename)

            # Snap boundaries to I-frames for lossless cutting
            start_time = qa_pair['total_start']
            end_time = qa_pair['total_end']

            snapped_start = self.iframe_extractor.find_nearest_iframe(
                start_time, iframe_timestamps, 'before'
            )
            snapped_end = self.iframe_extractor.find_nearest_iframe(
                end_time, iframe_timestamps, 'after'
            )

            # Add small padding for better context
            padding = 0.5  # 0.5 seconds padding
            snapped_start = max(0, snapped_start - padding)
            snapped_end = snapped_end + padding

            duration = snapped_end - snapped_start

            self.logger.info(f"Creating Q&A segment {segment_number}: {snapped_start:.2f}s - {snapped_end:.2f}s")

            # Step 1: Extract raw segment
            raw_segment_path = os.path.join(output_dir, f"raw_{segment_filename}")
            self._extract_raw_segment(video_path, raw_segment_path, snapped_start, duration)

            # Step 2: Apply vertical formatting and face detection if needed
            if output_format in ['vertical', 'square']:
                self._apply_formatting(
                    raw_segment_path, segment_path, output_format, params
                )
                # Clean up raw segment
                if os.path.exists(raw_segment_path):
                    os.remove(raw_segment_path)
            else:
                # For original format, just rename the raw segment
                os.rename(raw_segment_path, segment_path)

            # Generate metadata for the segment
            segment_metadata = {
                'segment_number': segment_number,
                'filename': segment_filename,
                'file_path': segment_path,
                'question_text': qa_pair['question_text'],
                'answer_text': qa_pair['answer_text'],
                'original_start': qa_pair['total_start'],
                'original_end': qa_pair['total_end'],
                'snapped_start': snapped_start,
                'snapped_end': snapped_end,
                'duration': duration,
                'quality_score': qa_pair['quality_score'],
                'output_format': output_format,
                'time_gap': qa_pair['time_gap']
            }

            self.logger.info(f"Successfully created Q&A segment: {segment_filename}")
            return segment_metadata

        except Exception as e:
            self.logger.error(f"Failed to create Q&A segment {segment_number}: {str(e)}")
            return None

    def _extract_raw_segment(self, video_path: str, output_path: str,
                           start_time: float, duration: float) -> None:
        """
        Extract raw video segment using FFmpeg

        Args:
            video_path: Path to source video
            output_path: Path for output segment
            start_time: Start time in seconds
            duration: Duration in seconds
        """
        try:
            (
                ffmpeg
                .input(video_path, ss=start_time)
                .output(
                    output_path,
                    t=duration,
                    vcodec='libx264',
                    acodec='aac',
                    preset='fast',
                    crf=23,
                    f='mp4',
                    movflags='+faststart'
                )
                .global_args('-y')
                .run(quiet=True, overwrite_output=True)
            )
        except Exception as e:
            self.logger.error(f"Failed to extract raw segment: {str(e)}")
            raise

    def _apply_formatting(self, input_path: str, output_path: str,
                         output_format: str, params: Dict[str, Any]) -> str:
        # Note: params is kept for future extensibility but not currently used
        """
        Apply vertical/square formatting and face detection to video segment

        Args:
            input_path: Path to input video segment
            output_path: Path for formatted output
            output_format: Output format (vertical or square)
            params: Additional parameters

        Returns:
            Path to formatted video
        """
        try:
            # Create a temporary clip data structure for the reframer
            # Need to generate a unique clip_id for the reframer
            import uuid
            clip_id = f"qa_segment_{str(uuid.uuid4())[:8]}"

            clip_data = {
                'file_path': input_path,
                'clip_id': clip_id,
                'start_time': 0,  # Already extracted, so start from 0
                'end_time': 0,    # Duration doesn't matter for reframing
                'duration': 0
            }

            # Get output directory from output path
            output_dir = os.path.dirname(output_path)

            # Use the existing reframer to apply formatting
            self.reframer._reframe_clip(clip_data, output_dir, output_format)

            # The reframer creates a file with _reframed.ts extension
            reframed_file = os.path.join(output_dir, f"{clip_id}_reframed.ts")

            # Convert from .ts to .mp4 and move to final location
            if os.path.exists(reframed_file):
                self._convert_ts_to_mp4(reframed_file, output_path)
                # Clean up temporary .ts file
                os.remove(reframed_file)
            else:
                # If reframing failed, copy the original
                import shutil
                shutil.copy2(input_path, output_path)

            return output_path

        except Exception as e:
            self.logger.error(f"Failed to apply formatting: {str(e)}")
            # If formatting fails, copy the original
            import shutil
            shutil.copy2(input_path, output_path)
            return output_path

    def _convert_ts_to_mp4(self, ts_path: str, mp4_path: str) -> None:
        """
        Convert transport stream file to MP4 format

        Args:
            ts_path: Path to input .ts file
            mp4_path: Path to output .mp4 file
        """
        try:
            (
                ffmpeg
                .input(ts_path)
                .output(
                    mp4_path,
                    vcodec='copy',  # Copy video stream without re-encoding
                    acodec='copy',  # Copy audio stream without re-encoding
                    f='mp4',
                    movflags='+faststart'
                )
                .global_args('-y')
                .run(quiet=True, overwrite_output=True)
            )
        except Exception as e:
            self.logger.error(f"Failed to convert TS to MP4: {str(e)}")
            # If conversion fails, just copy the .ts file
            import shutil
            shutil.copy2(ts_path, mp4_path)

    def _extract_qa_pairs_from_intelligent_highlights(self, qa_highlights_result: Dict[str, Any],
                                                     transcript_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract Q&A pairs from intelligent highlights result (Q&A-first approach)

        Args:
            qa_highlights_result: Result from intelligent highlights extractor with Q&A-first approach
            transcript_segments: List of transcript segments for reference

        Returns:
            List of Q&A pairs extracted from highlights
        """
        qa_pairs = []

        # Check if we have highlights with Q&A data
        highlights_path = qa_highlights_result.get('highlights_path')
        if not highlights_path or not os.path.exists(highlights_path):
            self.logger.warning("No highlights file found in Q&A-first result")
            return qa_pairs

        try:
            # Load highlights data
            with open(highlights_path, 'r') as f:
                highlights_data = json.load(f)

            highlights = highlights_data.get('highlights', [])
            self.logger.info(f"Loading {len(highlights)} highlights from Q&A-first approach")

            for i, highlight in enumerate(highlights):
                # Check if this highlight has Q&A data
                qa_data = highlight.get('qa_data')
                if not qa_data:
                    # Try to extract Q&A from highlight text if qa_data is missing
                    question = highlight.get('question', '')
                    answer = highlight.get('answer', '')

                    if question and answer:
                        qa_pair = {
                            'question_text': question,
                            'answer_text': answer,
                            'question_start': highlight['start_time'],
                            'question_end': highlight['start_time'] + 3.0,  # Estimate 3 seconds for question
                            'answer_start': highlight['start_time'] + 3.0,
                            'answer_end': highlight['end_time'],
                            'total_start': highlight['start_time'],
                            'total_end': highlight['end_time'],
                            'total_duration': highlight['duration'],
                            'time_gap': 0.0,  # Minimal gap in Q&A-first approach
                            'quality_score': highlight.get('score', 0.5),
                            'source': 'qa_first_highlight'
                        }
                        qa_pairs.append(qa_pair)
                else:
                    # Use the detailed Q&A data from intelligent highlights
                    qa_pair = {
                        'question_text': qa_data['question_text'],
                        'answer_text': qa_data['answer_text'],
                        'question_start': qa_data['question_start'],
                        'question_end': qa_data['question_end'],
                        'answer_start': qa_data['answer_start'],
                        'answer_end': qa_data['answer_end'],
                        'total_start': qa_data['total_start'],
                        'total_end': qa_data['total_end'],
                        'total_duration': qa_data['duration'],
                        'time_gap': qa_data.get('time_gap', 0.0),
                        'quality_score': qa_data['quality_score'],
                        'source': 'qa_first_detailed'
                    }
                    qa_pairs.append(qa_pair)

            self.logger.info(f"Extracted {len(qa_pairs)} Q&A pairs from Q&A-first highlights")
            return qa_pairs

        except Exception as e:
            self.logger.error(f"Failed to extract Q&A pairs from intelligent highlights: {str(e)}")
            return qa_pairs
