"""
Pipeline tasks for the Smart Video Highlight Generator
"""

from pipeline.tasks.preflight_validator import validate_video
from pipeline.tasks.video_ingestor import ingest_video
from pipeline.tasks.transcription_engine import transcribe_video
from pipeline.tasks.semantic_indexer import create_semantic_index
from pipeline.tasks.av_event_detector import detect_av_events
from pipeline.tasks.highlight_window_generator import generate_highlight_windows
# Intelligent highlights extractor moved to highlight_extraction module
# from pipeline.tasks.intelligent_highlights_extractor import extract_intelligent_highlights
from pipeline.tasks.shot_boundary_aligner import align_shot_boundaries
from pipeline.tasks.clip_renderer import render_clips
from pipeline.tasks.reframer import reframe_clips
from pipeline.tasks.caption_composer import compose_captions
from pipeline.tasks.deliverables_publisher import publish_deliverables
from pipeline.tasks.cleanup_metrics import cleanup_and_metrics
