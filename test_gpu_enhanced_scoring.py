#!/usr/bin/env python3
"""
Comprehensive test script for GPU-enhanced highlights scoring system
"""

import sys
import os
import time
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_gpu_enhanced_scoring():
    """Test the GPU-enhanced scoring system with comprehensive features"""
    print("🚀 Testing GPU-Enhanced Highlights Scoring System")
    print("=" * 60)
    
    try:
        # Import the enhanced scorer
        from highlight_extraction.utils.scoring import HighlightsScorer
        print("✅ Successfully imported GPU-enhanced HighlightsScorer")
        
        # Test different configurations
        configs = [
            {"enable_gpu": True, "batch_size": 16, "cache_size": 1000},
            {"enable_gpu": False, "batch_size": 8, "cache_size": 500}
        ]
        
        for i, config in enumerate(configs):
            print(f"\n🔧 Configuration {i+1}: {config}")
            
            # Initialize the scorer with specific config
            scorer = HighlightsScorer(**config)
            print(f"✅ Scorer initialized - Device: {scorer.gpu_config.get_device()}")
            
            # Test 1: Enhanced Q&A Detection with Semantic Analysis
            print("\n🔍 Test 1: Enhanced Q&A Detection")
            test_segments = [
                {'text': 'What are the key benefits of artificial intelligence in healthcare?', 'speaker': 'interviewer'},
                {'text': 'AI in healthcare offers tremendous benefits including improved diagnostic accuracy, personalized treatment plans, and faster drug discovery. Machine learning algorithms can analyze medical images with precision that often exceeds human capabilities.', 'speaker': 'expert'},
                {'text': 'That sounds fascinating. How does this impact patient outcomes?', 'speaker': 'interviewer'},
                {'text': 'Patient outcomes improve significantly through early detection of diseases, reduced medical errors, and more targeted therapies. AI helps doctors make better decisions faster.', 'speaker': 'expert'}
            ]
            
            start_time = time.time()
            qa_scores = scorer.detect_qa_patterns(test_segments)
            qa_time = time.time() - start_time
            print(f"   Q&A Detection completed in {qa_time*1000:.2f}ms")
            print(f"   Q&A Scores: {qa_scores}")
            
            # Test 2: Batch Processing for GPU Optimization
            print("\n⚡ Test 2: Batch Processing")
            batch_segments = [
                {'text': 'This is absolutely incredible and amazing!', 'speaker': 'speaker1'},
                {'text': 'The results are surprisingly good and exciting.', 'speaker': 'speaker2'},
                {'text': 'I am really thrilled about this breakthrough discovery.', 'speaker': 'speaker3'},
                {'text': 'This represents a significant advancement in the field.', 'speaker': 'speaker4'},
                {'text': 'The implications are far-reaching and transformative.', 'speaker': 'speaker5'}
            ]
            
            keywords = ["incredible", "breakthrough", "advancement", "transformative"]
            
            start_time = time.time()
            batch_results = scorer.batch_process_segments(batch_segments, keywords)
            batch_time = time.time() - start_time
            print(f"   Batch processing completed in {batch_time*1000:.2f}ms")
            print(f"   Processed {len(batch_segments)} segments")
            
            # Test 3: Enhanced Emotion Analysis with Ensemble Methods
            print("\n😊 Test 3: Enhanced Emotion Analysis")
            emotion_texts = [
                "This is absolutely fantastic and mind-blowing!",
                "I'm incredibly excited about these revolutionary findings.",
                "The breakthrough results are surprisingly impressive.",
                "This represents a remarkable achievement in science."
            ]
            
            emotion_scores = []
            start_time = time.time()
            for text in emotion_texts:
                score = scorer.calculate_emotion_intensity(text)
                emotion_scores.append(score)
            emotion_time = time.time() - start_time
            
            print(f"   Emotion analysis completed in {emotion_time*1000:.2f}ms")
            for text, score in zip(emotion_texts, emotion_scores):
                print(f"   '{text[:40]}...' -> {score:.3f}")
            
            # Test 4: Advanced Keyword Density with Semantic Similarity
            print("\n🔑 Test 4: Advanced Keyword Density")
            test_text = "Artificial intelligence and machine learning are revolutionizing healthcare through innovative AI solutions and intelligent automation systems."
            keywords = ["AI", "artificial intelligence", "machine learning", "healthcare", "innovation"]
            
            start_time = time.time()
            keyword_score = scorer.calculate_keyword_density(test_text, keywords)
            keyword_time = time.time() - start_time
            
            print(f"   Keyword analysis completed in {keyword_time*1000:.2f}ms")
            print(f"   Text: '{test_text}'")
            print(f"   Keywords: {keywords}")
            print(f"   Keyword Density Score: {keyword_score:.3f}")
            
            # Test 5: Novelty Scoring with Advanced Embeddings
            print("\n🆕 Test 5: Novelty Scoring")
            current_text = "Quantum computing represents a paradigm shift in computational capabilities."
            context_texts = [
                "We discussed artificial intelligence applications earlier.",
                "Machine learning algorithms were the focus of previous segments.",
                "The conversation covered traditional computing methods."
            ]
            
            start_time = time.time()
            novelty_score = scorer.calculate_novelty_score(current_text, context_texts)
            novelty_time = time.time() - start_time
            
            print(f"   Novelty analysis completed in {novelty_time*1000:.2f}ms")
            print(f"   Current: '{current_text}'")
            print(f"   Novelty Score: {novelty_score:.3f}")
            
            # Test 6: Performance Metrics and GPU Monitoring
            print("\n📊 Test 6: Performance Metrics")
            metrics = scorer.get_performance_metrics()
            
            print(f"   Cache Hit Rate: {metrics.get('cache_hit_rate', 0):.2%}")
            print(f"   GPU Memory Usage: {metrics.get('gpu_memory_usage', 0):.2f}GB")
            print(f"   Batch Processing Count: {metrics.get('batch_processing_count', 0)}")
            print(f"   Device: {metrics['gpu_config']['device']}")
            print(f"   GPU Available: {metrics['gpu_config']['gpu_available']}")
            
            # Display average inference times
            avg_times = metrics.get('avg_inference_times', {})
            if avg_times:
                print("   Average Inference Times:")
                for operation, timing in avg_times.items():
                    print(f"     {operation}: {timing['avg_ms']:.2f}ms (count: {timing['count']})")
            
            print(f"\n✅ Configuration {i+1} tests completed successfully!")
        
        print("\n🎉 All GPU-enhanced tests completed successfully!")
        print("✅ GPU acceleration working with automatic CPU fallback")
        print("✅ Batch processing optimized for GPU utilization")
        print("✅ Enhanced accuracy through ensemble methods")
        print("✅ Advanced caching and memory management")
        print("✅ Comprehensive performance monitoring")
        print("✅ Backward compatibility maintained")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_compatibility():
    """Test basic compatibility without heavy models"""
    print("\n🔧 Testing Basic Compatibility")
    print("-" * 40)
    
    try:
        from highlight_extraction.utils.scoring import HighlightsScorer, gpu_config
        
        # Test GPU configuration
        print(f"✅ GPU Config - Device: {gpu_config.device}")
        print(f"✅ GPU Available: {gpu_config.is_gpu_available()}")
        
        # Test basic initialization
        scorer = HighlightsScorer(enable_gpu=False, batch_size=4)
        print("✅ Basic scorer initialization successful")
        
        # Test pattern initialization
        print(f"✅ Q&A patterns: {len(scorer.qa_patterns)} categories")
        print(f"✅ Engagement patterns: {len(scorer.engagement_patterns)} categories")
        
        # Test basic methods
        is_question = scorer._is_question_segment("What is artificial intelligence?")
        print(f"✅ Question detection: {is_question}")
        
        engagement_score = scorer._calculate_engagement_score("This is absolutely amazing!")
        print(f"✅ Engagement scoring: {engagement_score:.3f}")
        
        composite_score = scorer.calculate_composite_score(0.8, 0.6, 0.7, 0.5)
        print(f"✅ Composite scoring: {composite_score:.3f}")
        
        print("✅ Basic compatibility tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic compatibility test failed: {e}")
        return False

if __name__ == "__main__":
    # Run basic compatibility test first
    basic_success = test_basic_compatibility()
    
    if basic_success:
        # Run full GPU-enhanced tests
        full_success = test_gpu_enhanced_scoring()
        sys.exit(0 if full_success else 1)
    else:
        sys.exit(1)
